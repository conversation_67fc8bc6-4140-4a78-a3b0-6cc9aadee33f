package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 交易对信息对象 exchange_pair_info
 */
@Data
@TableName("exchange_pair_info")
public class ExchangePairInfo {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 交易所名称 */
    private String exchangeName;

    /** 代币名称 */
    private String tokenName;

    /** 交易对名称 */
    private String pairName;

    /** Logo图片URL */
    private String logoUrl;

    /** API地址（可为空） */
    private String apiUrl;

    /** 是否特殊处理(0:否,1:是) */
    private Integer specialProcessing;

    /** 排序 */
    private Integer sort;

    /** 是否启用(0:禁用,1:启用) */
    private Integer isEnabled;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;
} 