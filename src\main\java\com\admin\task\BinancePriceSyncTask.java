package com.admin.task;

import com.admin.mapper.ExchangePairMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.concurrent.TimeUnit;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@Slf4j
@Component
public class BinancePriceSyncTask {

    @Autowired
    private ExchangePairMapper exchangePairInfoMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;



    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${binance.spot-ticker-url}")
    private String spotTickerUrl;

    @Value("${binance.futures-ticker-url}")
    private String futuresTickerUrl;

    /**
     * 每2秒同步币安现货启用交易对的24小时行情到Redis（JSON字符串）
     */
//    @Scheduled(fixedDelay = 3000)
    public void syncBinanceSpotTickers() {
        try {
            List<String> symbols = exchangePairInfoMapper.getEnabledPairNames();
            if (symbols == null || symbols.isEmpty()) {
                log.info("没有启用的交易对，无需同步币安行情");
                return;
            }
            for (String symbol : symbols) {
                String url = spotTickerUrl + symbol;
                try {
                    String resp = restTemplate.getForObject(url, String.class);
//                    log.info("symbol={}, resp={}", symbol, resp);
                    BinanceTickerResponse ticker = objectMapper.readValue(resp, BinanceTickerResponse.class);
                    if (ticker != null) {
                        String key = "binance:ticker:" + symbol;
                        String value = objectMapper.writeValueAsString(ticker);
                        stringRedisTemplate.opsForValue().set(key, value, 5, TimeUnit.SECONDS);
                    } else {
//                        log.warn("symbol {} 反序列化后为null，resp={}", symbol, resp);
                    }
                } catch (Exception ex) {
                    // log.warn("同步symbol {} 行情失败: {}", symbol, ex.getMessage(), ex);
                }
            }
//            log.info("同步币安现货行情到Redis成功，交易对数: {}", symbols.size());
        } catch (Exception e) {
            // log.error("同步币安现货行情到Redis失败", e);
        }
    }


    // 币安24hr行情返回结构（只列常用字段，可根据需要扩展）
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BinanceTickerResponse {
        public String symbol;
        public String priceChange;
        public String priceChangePercent;
        public String weightedAvgPrice;
        public String lastPrice;
        public String lastQty;
        public String openPrice;
        public String highPrice;
        public String lowPrice;
        public String volume;
        public String quoteVolume;
        public long openTime;
        public long closeTime;
        public String firstId;
        public String lastId;
        public String count;
        // ... 可根据实际需要补充字段
    }
} 