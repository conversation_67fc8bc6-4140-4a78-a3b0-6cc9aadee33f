package com.admin.mapper;

import com.admin.entity.RechargeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

@Mapper
public interface RechargeRecordMapper extends BaseMapper<RechargeRecord> {
    
    Map<String, Object> selectRechargeStatistics();

    List<Map<String, Object>> selectRechargeTrend(@org.apache.ibatis.annotations.Param("startDate") String startDate, @org.apache.ibatis.annotations.Param("endDate") String endDate);
} 