package com.admin.service.impl;

import com.admin.entity.UserWalletAddress;
import com.admin.vo.UserWalletAddressVO;
import com.admin.mapper.UserWalletAddressMapper;
import com.admin.service.IUserWalletAddressService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class UserWalletAddressServiceImpl extends ServiceImpl<UserWalletAddressMapper, UserWalletAddress> implements IUserWalletAddressService {
    
    @Override
    public Page<UserWalletAddress> selectUserWalletAddressPage(Page<UserWalletAddress> page, UserWalletAddress wallet) {
        return baseMapper.selectUserWalletAddressPage(page, wallet);
    }

    @Override
    public IPage<UserWalletAddressVO> selectWalletPage(Page<?> page, String chainName, String chainAddress, String username, String email, Long userId, Boolean usdtNotEmpty, Boolean bnbNotEmpty) {
        return baseMapper.selectWalletPage(page, chainName, chainAddress, username, email, userId, usdtNotEmpty, bnbNotEmpty);
    }

    @Override
    public UserWalletAddressVO selectWalletDetailById(Long id) {
        return baseMapper.selectWalletDetailById(id);
    }

    @Override
    public java.util.Map<String, Object> sumUsdtAndBnb(String chainName, String chainAddress, String username, String email, Long userId, Boolean usdtNotEmpty, Boolean bnbNotEmpty) {
        return baseMapper.sumUsdtAndBnb(chainName, chainAddress, username, email, userId, usdtNotEmpty, bnbNotEmpty);
    }
} 