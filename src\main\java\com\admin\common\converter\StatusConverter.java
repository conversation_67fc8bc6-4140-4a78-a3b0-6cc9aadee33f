package com.admin.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class StatusConverter implements Converter<Integer> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                   GlobalConfiguration globalConfiguration) {
        String value = cellData.getStringValue();
        switch (value) {
            case "待审核":
                return 0;
            case "已通过":
                return 1;
            case "已拒绝":
                return 2;
            case "未通过":
                return 3;
            default:
                return null;
        }
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
                                              GlobalConfiguration globalConfiguration) {
        String text;
        switch (value) {
            case 0:
                text = "待审核";
                break;
            case 1:
                text = "已通过";
                break;
            case 2:
                text = "已拒绝";
                break;
            case 3:
                text = "未通过";
                break;
            default:
                text = "未知";
        }
        return new WriteCellData<>(text);
    }
} 