package com.admin.service;

import com.admin.entity.SysJob;
import java.util.List;

public interface JobService {
    
    /**
     * 查询任务列表
     */
    List<SysJob> selectJobList(SysJob job);
    
    /**
     * 查询单个任务
     */
    SysJob selectJobById(Long jobId);
    
    /**
     * 新增任务
     */
    int insertJob(SysJob job);
    
    /**
     * 修改任务
     */
    int updateJob(SysJob job);
    
    /**
     * 删除任务
     */
    int deleteJobById(Long jobId);
    
    /**
     * 根据状态查询任务列表
     */
    List<SysJob> selectJobsByStatus(Integer status);
} 