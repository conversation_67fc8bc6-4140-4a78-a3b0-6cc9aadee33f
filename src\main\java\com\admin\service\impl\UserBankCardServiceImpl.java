package com.admin.service.impl;

import com.admin.entity.UserBankCard;
import com.admin.mapper.UserBankCardMapper;
import com.admin.service.UserBankCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class UserBankCardServiceImpl implements UserBankCardService {
    
    @Autowired
    private UserBankCardMapper userBankCardMapper;
    
    @Override
    public List<UserBankCard> getUserBankCards(Long userId) {
        return userBankCardMapper.selectByUserId(userId);
    }
} 