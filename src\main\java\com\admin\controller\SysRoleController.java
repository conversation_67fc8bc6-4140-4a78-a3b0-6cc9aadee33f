package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.SysRole;
import com.admin.service.SysRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/system/role")
public class SysRoleController {
    
    private static final Logger log = LoggerFactory.getLogger(SysRoleController.class);
    
    @Autowired
    private SysRoleService sysRoleService;

    @Log(title = "角色管理", operType = "查询")
    /**
     * 获取角色列表（包含所有角色，用于下拉选择）
     */
    @GetMapping("/list")
    public R list(@RequestParam(required = false) String name) {
        try {
            List<SysRole> roles = sysRoleService.getRoleList(name);
            return R.ok().data(roles);
        } catch (Exception e) {
            log.error("获取角色列表失败", e);
            return R.error(e.getMessage());
        }
    }
    
    /**
     * 新增角色
     */
    @Log(title = "角色管理", operType = "新增")
    @PostMapping
    public R add(@RequestBody SysRole role) {
        try {
            sysRoleService.addRole(role);
            return R.ok();
        } catch (Exception e) {
            log.error("新增角色失败", e);
            return R.error(e.getMessage());
        }
    }
    
    /**
     * 修改角色
     */
    @Log(title = "角色管理", operType = "修改")
    @PutMapping
    public R update(@RequestBody SysRole role) {
        try {
            sysRoleService.updateRole(role);
            return R.ok();
        } catch (Exception e) {
            log.error("修改角色失败", e);
            return R.error(e.getMessage());
        }
    }
    
    /**
     * 删除角色
     */
    @Log(title = "角色管理", operType = "删除")
    @DeleteMapping("/{id}")
    public R delete(@PathVariable Long id) {
        try {
            sysRoleService.deleteRole(id);
            return R.ok();
        } catch (Exception e) {
            log.error("删除角色失败", e);
            return R.error(e.getMessage());
        }
    }
    
    /**
     * 更新角色状态
     */
    @Log(title = "角色管理", operType = "修改")
    @PutMapping("/status")
    public R updateStatus(@RequestParam Long id, @RequestParam Integer status) {
        try {
            sysRoleService.updateRoleStatus(id, status);
            return R.ok();
        } catch (Exception e) {
            log.error("更新角色状态失败", e);
            return R.error(e.getMessage());
        }
    }
    
    /**
     * 分配权限
     */
    @PostMapping("/permissions")
    public R assignPermissions(@RequestBody Map<String, Object> params) {
        try {
            Long roleId = Long.valueOf(params.get("roleId").toString());
            List<Long> menuIds = (List<Long>) params.get("menuIds");
            sysRoleService.assignPermissions(roleId, menuIds);
            return R.ok();
        } catch (Exception e) {
            log.error("分配权限失败", e);
            return R.error(e.getMessage());
        }
    }
} 