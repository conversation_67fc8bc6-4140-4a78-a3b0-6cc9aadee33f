<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.FuturesOptionOrderMapper">
    <resultMap id="FuturesOptionOrderVOMap" type="com.admin.vo.FuturesOptionOrderVO">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="symbol" column="symbol"/>
        <result property="marginAmount" column="margin_amount"/>
        <result property="positionAmount" column="position_amount"/>
        <result property="lever" column="lever"/>
        <result property="direction" column="direction"/>
        <result property="takeProfit" column="take_profit"/>
        <result property="stopLoss" column="stop_loss"/>
        <result property="openPrice" column="open_price"/>
        <result property="closePrice" column="close_price"/>
        <result property="openTime" column="open_time"/>
        <result property="closeTime" column="close_time"/>
        <result property="status" column="status"/>
        <result property="profit" column="profit"/>
        <result property="profitStatus" column="profit_status"/>
        <result property="isSettlement" column="is_settlement"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 用户信息 -->
        <result property="userAvatar" column="user_avatar"/>
        <result property="userEmail" column="user_email"/>
        <result property="userNo" column="user_no"/>
    </resultMap>
    <select id="selectPageVO" resultMap="FuturesOptionOrderVOMap">
        SELECT f.*, u.avatar AS user_avatar, u.email AS user_email, u.user_no AS user_no
        FROM futures_option_order f
        LEFT JOIN front_user u ON f.user_id = u.id
        <where>
            <if test="params.symbol != null and params.symbol != ''">
                AND f.symbol = #{params.symbol}
            </if>
            <if test="params.userNo != null and params.userNo != ''">
                AND u.user_no = #{params.userNo}
            </if>
            <if test="params.isSettlement != null and params.isSettlement != ''">
                AND f.is_settlement = #{params.isSettlement}
            </if>
            <if test="params.status != null and params.status != ''">
                AND f.status = #{params.status}
            </if>
            <if test="params.profitStatus != null and params.profitStatus != ''">
                AND f.profit_status = #{params.profitStatus}
            </if>
            <if test="params.direction != null and params.direction != ''">
                AND f.direction = #{params.direction}
            </if>
        </where>
        ORDER BY f.id DESC
    </select>
    <select id="selectDetailVO" resultMap="FuturesOptionOrderVOMap">
        SELECT f.*, u.avatar AS user_avatar, u.email AS user_email, u.user_no AS user_no
        FROM futures_option_order f
        LEFT JOIN front_user u ON f.user_id = u.id
        WHERE f.id = #{id}
    </select>
</mapper> 