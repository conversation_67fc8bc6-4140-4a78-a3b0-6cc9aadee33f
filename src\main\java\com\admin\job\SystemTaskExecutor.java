package com.admin.job;

import com.admin.entity.*;
import com.admin.service.JobLogService;
import com.admin.service.FrontUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.List;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.admin.mapper.FrontUserMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.admin.mapper.CommissionRecordMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import com.admin.entity.UserWalletAddress;
import com.admin.mapper.UserWalletAddressMapper;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.utils.Convert;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.TypeReference;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import java.util.Arrays;
import java.util.Collections;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicInteger;
import com.admin.config.Web3BscProperties;

@Component("systemTask")
public class SystemTaskExecutor {

    private static final Logger log = LoggerFactory.getLogger(SystemTaskExecutor.class);

    @Autowired
    private JobLogService jobLogService;

    @Autowired
    private FrontUserService frontUserService;

    @Autowired
    private CommissionRecordMapper commissionRecordMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private UserWalletAddressMapper userWalletAddressMapper;

    @Autowired
    private Web3BscProperties web3BscProperties;

    // 轮询BSC RPC节点
    private List<String> bscRpcUrls;
    private AtomicInteger rpcIndex = new AtomicInteger(0);
    private String usdtContract;

    @PostConstruct
    public void initRpcUrls() {
        this.bscRpcUrls = web3BscProperties.getRpcUrls();
        this.usdtContract = web3BscProperties.getUsdtContract();
    }

    private String getNextRpcUrl() {
        if (bscRpcUrls == null || bscRpcUrls.isEmpty()) return null;
        int idx = rpcIndex.getAndUpdate(i -> (i + 1) % bscRpcUrls.size());
        return bscRpcUrls.get(idx);
    }

    private static final int MAX_MESSAGE_LENGTH = 500; // 设置消息最大长度

    /**
     * 每天零点执行的用户统计重置任务
     */
    public void resetTeamCount(Long jobId) {
        long startTime = System.currentTimeMillis();
        SysJobLog jobLog = new SysJobLog();
        jobLog.setJobId(jobId);
        jobLog.setJobName("用户统计重置任务");
        jobLog.setJobType("SYSTEM");
        jobLog.setExecutionTime(LocalDateTime.now());
        jobLog.setExecutionDuration(0);
        jobLog.setExecutionResult("运行中");
        jobLog.setExecutionMessage("任务开始执行");

        try {
            log.info("开始执行用户团队统计重置...");
            int count = frontUserService.resetTeamTodayCount();
            log.info("用户团队统计重置完成，重置了{}个用户", count);

            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult(count > 0 ? "成功" : "完成");
            String message = String.format("重置用户团队今日统计成功，共重置%d个用户", count);
            jobLog.setExecutionMessage(truncateMessage(message));

        } catch (Exception e) {
            log.error("用户团队统计重置执行异常", e);
            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult("失败");
            jobLog.setExecutionMessage(truncateMessage(e.getMessage()));
        } finally {
            jobLogService.save(jobLog);
        }
    }

    /**
     * 每天执行的用户业绩统计任务
     */
    public void updateUserPerformance(Long jobId) {
        long startTime = System.currentTimeMillis();
        SysJobLog jobLog = new SysJobLog();
        jobLog.setJobId(jobId);
        jobLog.setJobName("用户业绩统计任务");
        jobLog.setJobType("SYSTEM");
        jobLog.setExecutionTime(LocalDateTime.now());
        jobLog.setExecutionDuration(0);
        jobLog.setExecutionResult("运行中");
        jobLog.setExecutionMessage("任务开始执行");

        try {
            log.info("开始执行用户业绩统计...");

            // 1. 首先将所有用户的total_balance和team_total_count重置为0
            LambdaUpdateWrapper<FrontUser> resetWrapper = new LambdaUpdateWrapper<>();
            resetWrapper.set(FrontUser::getTeamTotalCount, BigDecimal.ZERO)
                       .set(FrontUser::getTeamTotalCount, 0);
            frontUserMapper.update(null, resetWrapper);
            log.info("重置所有用户业绩数据完成");

            // 2. 查询所有ID大于1的用户
            LambdaQueryWrapper<FrontUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.gt(FrontUser::getId, 0);
            List<FrontUser> users = frontUserService.list(userQuery);

            if (CollectionUtils.isEmpty(users)) {
                log.info("没有找到需要统计的用户");
                int duration = (int) (System.currentTimeMillis() - startTime);
                jobLog.setExecutionDuration(duration);
                jobLog.setExecutionMessage("没有找到需要统计的用户");
                jobLog.setExecutionResult("完成");
                return;
            }

            log.info("找到{}个用户需要统计业绩", users.size());

            // 3. 处理每个用户的业绩统计
            int processedCount = 0;
            for (FrontUser user : users) {
                try {
                    String referrerCode = user.getReferrerCode();
                    // 4. 递归更新所有上级的业绩
                    while (referrerCode != null && !referrerCode.isEmpty()) {
                        // 查询推荐人信息
                        FrontUser referrer = frontUserService.getOne(
                                new LambdaQueryWrapper<FrontUser>()
                                        .eq(FrontUser::getShareCode, referrerCode));

                        if (referrer == null) {
                            break;
                        }

                        // 更新推荐人的业绩数据
                        LambdaUpdateWrapper<FrontUser> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(FrontUser::getId, referrer.getId())
                                .setSql("team_total_count = team_total_count + 1");
                        frontUserService.update(updateWrapper);

                        // 继续查找上级推荐人
                        referrerCode = referrer.getReferrerCode();
                    }

                    processedCount++;
                    if (processedCount % 100 == 0) {
                        log.info("已处理{}个用户的业绩统计", processedCount);
                    }

                } catch (Exception e) {
                    log.error("处理用户{}的业绩统计时发生错误", user.getPhone(), e);
                }
            }

            log.info("用户业绩统计任务完成，成功处理{}个用户", processedCount);

            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult(processedCount > 0 ? "成功" : "完成");
            jobLog.setExecutionMessage(String.format("用户业绩统计任务执行成功，处理了%d个用户", processedCount));

        } catch (Exception e) {
            log.error("用户业绩统计任务执行异常", e);
            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult("失败");
            jobLog.setExecutionMessage(truncateMessage(e.getMessage()));
        } finally {
            jobLogService.save(jobLog);
        }
    }

    /**
     * 每天执行的数据清理任务
     */
    public void cleanHistoricalData(Long jobId) {
        long startTime = System.currentTimeMillis();
        SysJobLog jobLog = new SysJobLog();
        jobLog.setJobId(jobId);
        jobLog.setJobName("历史数据清理任务");
        jobLog.setJobType("SYSTEM");
        jobLog.setExecutionTime(LocalDateTime.now());
        jobLog.setExecutionDuration(0);
        jobLog.setExecutionResult("运行中");
        jobLog.setExecutionMessage("任务开始执行");

        try {
            log.info("开始执行历史数据清理...");
            int totalDeleted = 0;

            // 1. 清理commission_record表
            int commissionDeleted = commissionRecordMapper.delete(
                new LambdaQueryWrapper<CommissionRecord>()
                    .lt(CommissionRecord::getCreateTime, 
                        LocalDateTime.now().minusDays(10))
            );
            log.info("清理commission_record表完成，删除{}条记录", commissionDeleted);



            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult(totalDeleted > 0 ? "成功" : "完成");
            jobLog.setExecutionMessage(
                String.format("历史数据清理任务执行成功，共清理%d条记录(佣金记录:%d)",
                    totalDeleted, commissionDeleted));

        } catch (Exception e) {
            log.error("历史数据清理任务执行异常", e);
            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult("失败");
            jobLog.setExecutionMessage(truncateMessage(e.getMessage()));
        } finally {
            jobLogService.save(jobLog);
        }
    }

    

    /**
     * 兼容调度器反射调用（带jobId参数），并记录日志
     */
 
    public void syncWalletBalances(Long jobId) {
        // 强制每次从配置类读取usdtContract，防止被覆盖
        this.usdtContract = "******************************************";
        log.info("syncWalletBalances方法启动，usdtContract当前值={}", usdtContract);
        long startTime = System.currentTimeMillis();
        SysJobLog jobLog = new SysJobLog();
        jobLog.setJobId(jobId);
        jobLog.setJobName("钱包余额同步任务");
        jobLog.setJobType("SYSTEM");
        jobLog.setExecutionTime(LocalDateTime.now());
        jobLog.setExecutionDuration(0);
        jobLog.setExecutionResult("运行中");
        jobLog.setExecutionMessage("任务开始执行");

        int successCount = 0;
        int failCount = 0;
        try {
            log.info("开始同步user_wallet_address表BNB和USDT余额...");
            List<UserWalletAddress> wallets = userWalletAddressMapper.selectList(null);
            for (UserWalletAddress wallet : wallets) {
                String address = wallet.getChainAddress();
                boolean success = false;
                Exception lastEx = null;
                for (int i = 0; i < bscRpcUrls.size(); i++) {
                    String rpcUrl = getNextRpcUrl();
                    Web3j web3j = Web3j.build(new HttpService(rpcUrl));
                    try {
                        BigDecimal bnbBalance = getBnbBalance(web3j, address);
                        log.info("调用getErc20Balance, 合约={}, 用户地址={}", usdtContract, address);
                        BigDecimal usdtBalance = getErc20Balance(web3j, usdtContract, address);
                        wallet.setBnbBalance(bnbBalance);
                        wallet.setUsdtBalance(usdtBalance);
                        userWalletAddressMapper.updateById(wallet);
                        success = true;
                        web3j.shutdown();
                        break;
                    } catch (Exception e) {
                        lastEx = e;
                        log.warn("节点{}查询{}失败，尝试下一个...", rpcUrl, address);
                        web3j.shutdown();
                    }
                }
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                    log.error("所有BSC节点查询{}失败", address, lastEx);
                }
            }
            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult("成功");
            jobLog.setExecutionMessage(String.format("同步完成，成功%d个，失败%d个", successCount, failCount));
        } catch (Exception e) {
            int duration = (int) (System.currentTimeMillis() - startTime);
            jobLog.setExecutionDuration(duration);
            jobLog.setExecutionResult("失败");
            jobLog.setExecutionMessage(truncateMessage(e.getMessage()));
            log.error("钱包余额同步任务异常", e);
        } finally {
            jobLogService.save(jobLog);
        }
    }

    /**
     * 查询BNB余额
     */
    private BigDecimal getBnbBalance(Web3j web3j, String address) {
        try {
            return Convert.fromWei(web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST)
                    .send().getBalance().toString(), Convert.Unit.ETHER);
        } catch (Exception e) {
            log.error("查询BNB余额失败:{}", address, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 查询ERC20代币余额（如USDT）
     */
    private BigDecimal getErc20Balance(Web3j web3j, String contract, String address) {
        try {
            // 防御性处理钱包地址
            String cleanAddress = address == null ? "" : address.trim();
            if (cleanAddress.startsWith("0x") || cleanAddress.startsWith("0X")) {
                cleanAddress = cleanAddress.substring(2);
            }
            if (cleanAddress.length() > 40) {
                cleanAddress = cleanAddress.substring(0, 40);
            }
            

            // 防御性处理合约地址
            String contractAddr = contract == null ? "" : contract.trim();
            if (contractAddr.startsWith("0x") || contractAddr.startsWith("0X")) {
                contractAddr = contractAddr.substring(2);
            }
            contractAddr = "0x" + contractAddr;
            if (contractAddr.length() > 42) {
                contractAddr = contractAddr.substring(0, 42);
            }
            

            Function function = new Function(
                    "balanceOf",
                    Collections.singletonList(new Address(cleanAddress)),
                    Collections.singletonList(new TypeReference<Uint256>() {})
            );
            String data = FunctionEncoder.encode(function);
            String fromAddr = "0x" + cleanAddress;
            EthCall response = web3j.ethCall(
                    Transaction.createEthCallTransaction(fromAddr, contractAddr, data),
                    DefaultBlockParameterName.LATEST).send();

            if (response.getError() != null) {
                log.error("USDT余额查询节点返回错误, address={}, code={}, message={}, data={}",
                        fromAddr,
                        response.getError().getCode(),
                        response.getError().getMessage(),
                        response.getError().getData());
            }
            

            List<Type> results = org.web3j.abi.FunctionReturnDecoder.decode(
                    response.getValue(), function.getOutputParameters());
            if (!results.isEmpty()) {
                BigDecimal raw = new BigDecimal(results.get(0).getValue().toString());
               
                // BSC主网USDT为18位小数，转化为可读数量
                BigDecimal usdt = raw.movePointLeft(18);
               
                return usdt;
            } else {
               
            }
        } catch (Exception e) {
            log.error("查询USDT余额失败:{}", address, e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 截断消息到指定长度
     */
    private String truncateMessage(String message) {
        if (message == null) {
            return "执行异常";
        }
        return message.length() > MAX_MESSAGE_LENGTH ? message.substring(0, MAX_MESSAGE_LENGTH) : message;
    }

}