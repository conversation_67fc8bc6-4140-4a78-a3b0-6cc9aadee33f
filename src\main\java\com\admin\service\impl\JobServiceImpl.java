package com.admin.service.impl;

import com.admin.entity.SysJob;
import com.admin.service.JobService;
import com.admin.mapper.SysJobMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class JobServiceImpl implements JobService {

    @Autowired
    private SysJobMapper jobMapper;
    
    @Override
    public List<SysJob> selectJobList(SysJob job) {
        return jobMapper.selectJobList(job);
    }
    
    @Override
    public SysJob selectJobById(Long jobId) {
        return jobMapper.selectJobById(jobId);
    }
    
    @Override
    public int insertJob(SysJob job) {
        return jobMapper.insertJob(job);
    }
    
    @Override
    public int updateJob(SysJob job) {
        return jobMapper.updateJob(job);
    }
    
    @Override
    public int deleteJobById(Long jobId) {
        return jobMapper.deleteJobById(jobId);
    }
    
    @Override
    public List<SysJob> selectJobsByStatus(Integer status) {
        QueryWrapper<SysJob> wrapper = new QueryWrapper<>();
        wrapper.eq("status", status);
        return jobMapper.selectList(wrapper);
    }
} 