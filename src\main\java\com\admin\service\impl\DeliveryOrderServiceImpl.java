package com.admin.service.impl;

import com.admin.entity.DeliveryOrder;
import com.admin.entity.FrontUser;
import com.admin.mapper.DeliveryOrderMapper;
import com.admin.mapper.FrontUserMapper;
import com.admin.service.DeliveryOrderService;
import com.admin.service.TradeRecordService;
import com.admin.vo.DeliveryOrderVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DeliveryOrderServiceImpl implements DeliveryOrderService {
    @Resource
    private DeliveryOrderMapper deliveryOrderMapper;

    @Resource
    private FrontUserMapper frontUserMapper;

    @Resource
    private TradeRecordService tradeRecordService;

    @Override
    public IPage<DeliveryOrderVO> selectPageVO(Map<String, Object> params) {
        int pageNum = params.get("pageNum") == null ? 1 : Integer.parseInt(params.get("pageNum").toString());
        int pageSize = params.get("pageSize") == null ? 10 : Integer.parseInt(params.get("pageSize").toString());

        Page<DeliveryOrderVO> page = new Page<>(pageNum, pageSize);
        return deliveryOrderMapper.selectPageVO(page, params);
    }

    @Override
    public DeliveryOrderVO selectDetailVO(Long id) {
        return deliveryOrderMapper.selectDetailVO(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> oneClickReplenish() {
        log.info("开始执行一键补仓操作");

        // 查询需要补仓的订单
        List<DeliveryOrder> replenishOrders = deliveryOrderMapper.selectReplenishOrders();

        if (replenishOrders.isEmpty()) {
            log.info("没有需要补仓的订单");
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "没有需要补仓的订单");
            result.put("replenishCount", 0);
            result.put("totalAmount", BigDecimal.ZERO);
            return result;
        }

        int successCount = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (DeliveryOrder order : replenishOrders) {
            try {
                // 处理单个订单的补仓
                if (processReplenishOrder(order)) {
                    successCount++;
                    // profit是负数，所以取绝对值作为补仓金额
                    BigDecimal replenishAmount = order.getProfit().abs();
                    totalAmount = totalAmount.add(replenishAmount);
                }
            } catch (Exception e) {
                log.error("处理订单补仓失败，订单ID: {}, 错误: {}", order.getId(), e.getMessage(), e);
            }
        }

        log.info("一键补仓完成，成功处理 {} 个订单，总补仓金额: {}", successCount, totalAmount);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", String.format("补仓成功！共处理 %d 个订单，总补仓金额: %s USDT", successCount, totalAmount));
        result.put("replenishCount", successCount);
        result.put("totalAmount", totalAmount);
        return result;
    }

    /**
     * 处理单个订单的补仓
     */
    private boolean processReplenishOrder(DeliveryOrder order) {
        log.info("处理订单补仓，订单ID: {}, 用户ID: {}, 带单员ID: {}, profit值: {}",
                order.getId(), order.getUserId(), order.getLeaderId(), order.getProfit());

        // 获取带单员信息
        FrontUser leader = frontUserMapper.selectById(order.getLeaderId());
        if (leader == null) {
            log.error("带单员不存在，ID: {}", order.getLeaderId());
            return false;
        }

        // 获取跟单用户信息
        FrontUser user = frontUserMapper.selectById(order.getUserId());
        if (user == null) {
            log.error("用户不存在，ID: {}", order.getUserId());
            return false;
        }

        // profit是负数（亏损），例如 profit = -100.00
        // 需要从带单员储备金中扣除100.00，给跟单用户补偿100.00
        BigDecimal lossAmount = order.getProfit().abs(); // 获取亏损金额的绝对值，例如 100.00

        // 检查带单员储备金是否足够
        BigDecimal currentReserve = leader.getReserveAmount() != null ? leader.getReserveAmount() : BigDecimal.ZERO;
        if (currentReserve.compareTo(lossAmount) < 0) {
            log.error("带单员储备金不足，当前储备金: {}, 需要扣除: {}", currentReserve, lossAmount);
            return false;
        }

        // 1. 从带单员储备金中扣除亏损金额（减少储备金）
        // 例如：lossAmount = 100.00，deductAmount = -100.00
        // SQL: reserve_amount = reserve_amount + (-100.00) = reserve_amount - 100.00
        BigDecimal deductAmount = lossAmount.negate(); // 转为负数进行扣除
        int leaderUpdateResult = frontUserMapper.updateReserveAmount(order.getLeaderId(), deductAmount);
        if (leaderUpdateResult <= 0) {
            log.error("更新带单员储备金失败，带单员ID: {}", order.getLeaderId());
            return false;
        }
        log.info("从带单员储备金扣除: {} USDT，带单员ID: {}", lossAmount, order.getLeaderId());

        // 2. 将亏损金额补偿到跟单用户的跟单账户（增加跟单余额）
        // 例如：lossAmount = 100.00
        // SQL: copy_trade_balance = copy_trade_balance + 100.00
        int userUpdateResult = frontUserMapper.updateCopyTradeBalance(order.getUserId(), lossAmount);
        if (userUpdateResult <= 0) {
            log.error("更新用户跟单账户余额失败，用户ID: {}", order.getUserId());
            return false;
        }
        log.info("向跟单用户补偿: {} USDT，用户ID: {}", lossAmount, order.getUserId());

        // 3. 添加交易明细记录
        String remark = String.format("一键补仓 - 订单ID:%d，补仓金额:%s USDT", order.getId(), lossAmount);
        tradeRecordService.addTradeRecord(
            order.getUserId(),
            user.getUsername(),
            "一键补仓",
            lossAmount,
            2, // 跟单账户
            remark
        );

        // 4. 更新订单的返利状态为已返利(2)
        int updateRebateResult = deliveryOrderMapper.updateRebateStatus(order.getId(), 2);
        if (updateRebateResult <= 0) {
            log.error("更新订单返利状态失败，订单ID: {}", order.getId());
            return false;
        }
        log.info("更新订单返利状态成功，订单ID: {}, 返利状态: 已返利", order.getId());

        log.info("订单补仓成功，订单ID: {}, 补仓金额: {}", order.getId(), lossAmount);
        return true;
    }
}