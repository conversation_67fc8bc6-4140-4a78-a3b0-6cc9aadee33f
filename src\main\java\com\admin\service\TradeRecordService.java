package com.admin.service;

import com.admin.entity.TradeRecord;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.Map;

public interface TradeRecordService extends IService<TradeRecord> {
    IPage<TradeRecord> pageTradeRecord(Page<TradeRecord> page, Map<String, Object> params);
    /**
     * 写入交易明细
     */
    void addTradeRecord(Long userId, String username, String tradeType, java.math.BigDecimal amount, Integer accountType, String remark);
} 