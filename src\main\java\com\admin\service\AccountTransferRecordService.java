package com.admin.service;

import com.admin.entity.AccountTransferRecord;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.Map;

public interface AccountTransferRecordService extends IService<AccountTransferRecord> {
    IPage<AccountTransferRecord> pageAccountTransferRecord(Page<AccountTransferRecord> page, Map<String, Object> params);
} 