package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.common.utils.SecurityUtils;
import com.admin.entity.SysUser;
import com.admin.service.SysUserService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

@RestController
@RequestMapping("/system/user")
public class SysUserController {

    private static final Logger log = LoggerFactory.getLogger(SysUserController.class);

    @Autowired
    private SysUserService sysUserService;

    /**
     * 获取用户列表
     */
    @Log(title = "用户管理", operType = "查询")
    @GetMapping("/list")
    public R list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) Integer status
    ) {
        IPage<SysUser> page = sysUserService.getUserPage(pageNum, pageSize, username, status);
        return R.ok().put("data", new HashMap<String, Object>() {{
            put("records", page.getRecords());
            put("total", page.getTotal());
        }});
    }

    
    /**
     * 新增用户
     */
    @Log(title = "用户管理", operType = "新增")
    @PostMapping
    public R add(@RequestBody SysUser user) {
        // 参数校验
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            return R.error("用户名不能为空");
        }
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            return R.error("密码不能为空");
        }
        
        try {
            // 获取当前登录用户
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = sysUserService.getUserById(currentUserId);
            
            // 判断是否为管理员
            if (!"admin".equals(currentUser.getRole())) {
                return R.error("只有管理员才能添加用户");
            }
            
            // 检查用户名是否已存在
            SysUser existingUser = sysUserService.getUserByUsername(user.getUsername());
            if (existingUser != null) {
                return R.error("用户已存在，请更换其他用户名");
            }
            
            sysUserService.addUser(user);
            return R.ok();
        } catch (Exception e) {
            log.error("新增用户失败", e);
            return R.error(e.getMessage());
        }
    }

    /**
     * 修改用户
     */
    @Log(title = "用户管理", operType = "修改")
    @PutMapping
    public R update(@RequestBody SysUser user) {
        if (user.getId() == null) {
            return R.error("用户ID不能为空");
        }
        
        try {
            // 更新用户基本信息
            sysUserService.updateUser(user);
            return R.ok();
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return R.error(e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @Log(title = "用户管理", operType = "删除")
    @DeleteMapping("/{id}")
    public R delete(@PathVariable Long id) {
        if (id == null) {
            return R.error("用户ID不能为空");
        }
        
        try {
            // 获取当前登录用户
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = sysUserService.getUserById(currentUserId);
            
            // 判断是否为管理员
            if (!"admin".equals(currentUser.getRole())) {
                return R.error("只有管理员才能删除用户");
            }
            
            // 不能删除自己
            if (id.equals(currentUserId)) {
                return R.error("不能删除当前登录用户");
            }
            
            sysUserService.deleteUser(id);
            return R.ok();
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return R.error(e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @Log(title = "用户管理", operType = "状态修改")
    @PutMapping("/status")
    public R updateStatus(@RequestParam Long id, @RequestParam Integer status) {
        if (id == null) {
            return R.error("用户ID不能为空");
        }
        if (status == null) {
            return R.error("状态不能为空");
        }
        
        try {
            sysUserService.updateUserStatus(id, status);
            return R.ok();
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 重置密码
     */
    @Log(title = "用户管理", operType = "重置密码")
    @PostMapping("/reset-password/{id}")
    public R resetPassword(@PathVariable Long id) {
        if (id == null) {
            return R.error("用户ID不能为空");
        }
        
        try {
            // 获取当前登录用户
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = sysUserService.getUserById(currentUserId);
            
            // 判断是否为管理员
            if (!"admin".equals(currentUser.getRole())) {
                return R.error("只有管理员才能重置密码");
            }
            
            // 不能重置自己的密码
            if (id.equals(currentUserId)) {
                return R.error("不能重置当前登录用户的密码");
            }
            
            String newPassword = "123456"; // 默认重置密码
            sysUserService.resetPassword(id, newPassword);
            
            // 确保返回格式正确
            return R.ok().put("data", new HashMap<String, Object>() {{
                put("password", newPassword);
            }});
        } catch (Exception e) {
            log.error("重置密码失败: {}", e.getMessage(), e);
            return R.error("重置密码失败：" + e.getMessage());
        }
    }
} 