package com.admin.service.impl;

import com.admin.entity.TradeRecord;
import com.admin.mapper.TradeRecordMapper;
import com.admin.service.TradeRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.Map;

@Service
public class TradeRecordServiceImpl extends ServiceImpl<TradeRecordMapper, TradeRecord> implements TradeRecordService {
    @Override
    public IPage<TradeRecord> pageTradeRecord(Page<TradeRecord> page, Map<String, Object> params) {
        return baseMapper.selectTradeRecordList(page, params);
    }

    @Override
    public void addTradeRecord(Long userId, String username, String tradeType, java.math.BigDecimal amount, Integer accountType, String remark) {
        baseMapper.insertTradeRecord(userId, username, tradeType, amount, accountType, remark);
    }
} 