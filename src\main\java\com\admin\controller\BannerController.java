package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.Banner;
import com.admin.service.BannerService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/banner")
public class BannerController {

    @Autowired
    private BannerService bannerService;
    @Log(title = "轮播图列表", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam(required = false) String title,
                 @RequestParam(required = false) Integer status,
                 @RequestParam(defaultValue = "1") Integer page,
                 @RequestParam(defaultValue = "10") Integer limit) {
        try {
            IPage<Banner> pageData = bannerService.getBannerList(title, status, page, limit);
            return R.ok()
                    .put("data", pageData.getRecords())
                    .put("total", pageData.getTotal());
        } catch (Exception e) {
            log.error("获取轮播图列表失败", e);
            return R.error("获取轮播图列表失败");
        }
    }
    @Log(title = "轮播图列表", operType = "新增")
    @PostMapping("/add")
    public R add(@RequestBody Banner banner) {
        try {
            if (bannerService.addBanner(banner)) {
                return R.ok();
            }
            return R.error("新增轮播图失败");
        } catch (Exception e) {
            log.error("新增轮播图失败", e);
            return R.error("新增轮播图失败");
        }
    }
    @Log(title = "轮播图列表", operType = "修改")
    @PutMapping("/update")
    public R update(@RequestBody Banner banner) {
        try {
            if (bannerService.updateBanner(banner)) {
                return R.ok();
            }
            return R.error("修改轮播图失败");
        } catch (Exception e) {
            log.error("修改轮播图失败", e);
            return R.error("修改轮播图失败");
        }
    }
    @Log(title = "轮播图列表", operType = "修改")
    @PutMapping("/status/{id}/{status}")
    public R toggleStatus(@PathVariable Long id, @PathVariable Integer status) {
        try {
            if (bannerService.toggleStatus(id, status)) {
                return R.ok();
            }
            return R.error("修改状态失败");
        } catch (Exception e) {
            log.error("修改状态失败", e);
            return R.error("修改状态失败");
        }
    }

    @DeleteMapping("/delete/{id}")
    public R delete(@PathVariable Long id) {
        try {
            if (bannerService.deleteBanner(id)) {
                return R.ok();
            }
            return R.error("删除轮播图失败");
        } catch (Exception e) {
            log.error("删除轮播图失败", e);
            return R.error("删除轮播图失败");
        }
    }
} 