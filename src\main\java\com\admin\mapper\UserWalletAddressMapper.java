package com.admin.mapper;

import com.admin.entity.UserWalletAddress;
import com.admin.vo.UserWalletAddressVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface UserWalletAddressMapper extends BaseMapper<UserWalletAddress> {
    
    /**
     * 分页查询用户钱包地址列表
     */
    Page<UserWalletAddress> selectUserWalletAddressPage(Page<UserWalletAddress> page, @Param("wallet") UserWalletAddress wallet);

    /**
     * 分页查询用户钱包地址列表（带用户名和手机号，全部精确查询）
     */
    IPage<UserWalletAddressVO> selectWalletPage(Page<?> page,
        @Param("chainName") String chainName,
        @Param("chainAddress") String chainAddress,
        @Param("username") String username,
        @Param("email") String email,
        @Param("userId") Long userId,
        @Param("usdtNotEmpty") Boolean usdtNotEmpty,
        @Param("bnbNotEmpty") Boolean bnbNotEmpty);

    /**
     * 根据ID查询钱包详情（带用户名和手机号）
     */
    UserWalletAddressVO selectWalletDetailById(@Param("id") Long id);

    /**
     * 统计USDT和BNB总和（可带条件）
     */
    Map<String, Object> sumUsdtAndBnb(@Param("chainName") String chainName,
                                      @Param("chainAddress") String chainAddress,
                                      @Param("username") String username,
                                      @Param("email") String email,
                                      @Param("userId") Long userId,
                                      @Param("usdtNotEmpty") Boolean usdtNotEmpty,
                                      @Param("bnbNotEmpty") Boolean bnbNotEmpty);

    UserWalletAddress findByAddress(@Param("chainAddress") String chainAddress);
} 