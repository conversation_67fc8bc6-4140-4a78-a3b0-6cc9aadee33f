package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.admin.common.converter.StatusConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.admin.common.converter.IndexConverter;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;

@Data
@TableName("withdraw_record")
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 42)
@HeadRowHeight(25)
public class WithdrawRecord {
    @TableId(type = IdType.AUTO)
    @ExcelIgnore
    private Long id;
    
    @ExcelIgnore
    private Long userId;
    
    @ExcelProperty(value = "序号", converter = IndexConverter.class)
    @ColumnWidth(8)
    @ExcelIgnore
    private transient Integer index;
    
    @ExcelProperty(value = "用户名")
    @ColumnWidth(15)
    private String username;
    
    @ExcelProperty(value = "注册邮箱")
    @ColumnWidth(20)
    private String registerEmail;
    
    @ExcelProperty(value = "提现金额")
    @ColumnWidth(15)
    private BigDecimal amount;
    
    @ExcelProperty(value = "手续费")
    @ColumnWidth(15)
    private BigDecimal fee;
    
    @ExcelProperty(value = "实际到账")
    @ColumnWidth(15)
    private BigDecimal realAmount;
    
    @ExcelProperty(value = "提现地址")
    @ColumnWidth(40)
    private String address;
    
    @ExcelProperty(value = "链名称")
    @ColumnWidth(15)
    private String chainName;
    
    @ExcelProperty(value = "链上哈希")
    @ColumnWidth(40)
    private String txHash;
    
    @ExcelProperty(value = "状态", converter = StatusConverter.class)
    @ColumnWidth(10)
    private Integer status;
    
    @ExcelProperty(value = "备注")
    @ColumnWidth(30)
    private String remark;
    
    @ExcelProperty(value = "申请时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;
    
    @ExcelProperty(value = "更新时间")
    @ColumnWidth(20)
    private LocalDateTime updateTime;
} 