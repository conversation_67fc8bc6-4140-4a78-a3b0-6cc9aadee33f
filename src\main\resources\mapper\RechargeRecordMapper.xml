<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.RechargeRecordMapper">

    <!-- 只保留统计查询 -->
    <select id="selectRechargeStatistics" resultType="java.util.Map">
        SELECT 
            SUM(CASE WHEN recharge_type = 2 THEN amount ELSE 0 END) as adminTotal,
            SUM(CASE WHEN recharge_type = 1 THEN amount ELSE 0 END) as userTotal,
            SUM(CASE WHEN audit_status = 0 THEN amount ELSE 0 END) as pendingTotal
        FROM recharge_record where  audit_status=0 or audit_status=1
    </select>
<!-- 充值趋势图：统计每天充值总额 -->
<select id="selectRechargeTrend" resultType="map">
    SELECT
        DATE_FORMAT(create_time, '%Y-%m-%d') AS date,
        SUM(amount) AS amount
    FROM
        recharge_record
    WHERE
        create_time BETWEEN  CONCAT(#{startDate}, ' 00:00:00') AND CONCAT(#{endDate}, ' 23:59:59')  
        AND audit_status = 1
    GROUP BY
        DATE_FORMAT(create_time, '%Y-%m-%d')
    ORDER BY
        date ASC
</select>
</mapper> 