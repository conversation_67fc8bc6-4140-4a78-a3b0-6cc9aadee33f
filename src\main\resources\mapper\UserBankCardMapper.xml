<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.UserBankCardMapper">
    
    <select id="selectByUserId" resultType="com.admin.entity.UserBankCard">
        SELECT 
            id,
            user_id,
            chain_name,
            address,
            is_default as isDefault,
            status,
            create_time,
            update_time
        FROM user_bank_card
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>
    
</mapper> 