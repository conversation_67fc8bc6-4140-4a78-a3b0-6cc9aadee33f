<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.AccountTransferRecordMapper">

    <resultMap id="BaseResultMap" type="com.admin.entity.AccountTransferRecord">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="from_account_type" property="fromAccountType" />
        <result column="to_account_type" property="toAccountType" />
        <result column="amount" property="amount" />
        <result column="from_balance_before" property="fromBalanceBefore" />
        <result column="from_balance_after" property="fromBalanceAfter" />
        <result column="to_balance_before" property="toBalanceBefore" />
        <result column="to_balance_after" property="toBalanceAfter" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="email" property="email" />
    </resultMap>

    <select id="selectAccountTransferRecordList" resultMap="BaseResultMap">
        SELECT t.*, u.email
        FROM account_transfer_record t
        LEFT JOIN front_user u ON t.user_id = u.id
        <where>
            <if test="params.username != null and params.username != ''">
                AND t.username = #{params.username}
            </if>
            <if test="params.email != null and params.email != ''">
                AND u.email = #{params.email}
            </if>
            <if test="params.fromAccountType != null and params.fromAccountType != ''">
                AND t.from_account_type = #{params.fromAccountType}
            </if>
            <if test="params.toAccountType != null and params.toAccountType != ''">
                AND t.to_account_type = #{params.toAccountType}
            </if>
            <if test="params.status != null and params.status != ''">
                AND t.status = #{params.status}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper> 