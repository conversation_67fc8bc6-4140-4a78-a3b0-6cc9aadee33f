package com.admin.service;

import com.admin.entity.SysUser;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface SysUserService extends IService<SysUser> {
    IPage<SysUser> getUserPage(Integer pageNum, Integer pageSize, String username, Integer status);
    
    void addUser(SysUser user);
    
    void updateUser(SysUser user);
    
    void deleteUser(Long id);
    
    void updateUserStatus(Long id, Integer status);
    
    /**
     * 重置用户密码
     */
    void resetPassword(Long id, String newPassword);
    
    SysUser getUserById(Long id);
    
    SysUser getUserByUsername(String username);
} 