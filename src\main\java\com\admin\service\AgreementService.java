package com.admin.service;

import com.admin.entity.Agreement;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

public interface AgreementService extends IService<Agreement> {
    
    IPage<Agreement> getAgreementList(String title, Integer page, Integer limit);
    
    boolean addAgreement(Agreement agreement);
    
    boolean updateAgreement(Agreement agreement);
    
    boolean deleteAgreement(Long id);
} 