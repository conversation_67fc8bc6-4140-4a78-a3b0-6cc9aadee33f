package com.admin.task;

import com.admin.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
 
import com.admin.service.SysParamsService;
 


import com.admin.mapper.WithdrawRecordMapper;
import com.admin.service.ChainConfigService;
import com.admin.service.WithdrawRecordService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Component
@Slf4j
public class MinWithdrawMonitorTask {   
    
    @Autowired
    private SysParamsService sysParamsService;
    
    @Autowired
    private WithdrawRecordMapper withdrawRecordMapper;
    
    @Autowired
    private ChainConfigService chainConfigService;
    
    @Autowired
    private WithdrawRecordService withdrawRecordService;
    
    private volatile boolean running = false;
    private Thread monitorThread;

    @PostConstruct
    public void init() {
        start();
    }

    @PreDestroy
    public void destroy() {
        stop();
    }

    public void start() {
        if (!running) {
            running = true;
            monitorThread = new Thread(this::monitorTask, "order-monitor-thread");
            monitorThread.start();
            log.info("小笔提现监控任务已启动");
        }
    }

    public void stop() {
        running = false;
        if (monitorThread != null) {
            monitorThread.interrupt();
        }
        log.info("小笔提现监控任务已停止");
    }

    private void monitorTask() {
        while (running) {
            try {
                
                // 获取系统参数
                SysParams sysParams = sysParamsService.getParams();
                if (sysParams == null) {
                    log.error("未找到系统参数配置");
                    Thread.sleep(5000);
                    continue;
                }
                
                //是否允许自动提现
                Integer autoWithdraw = sysParams.getAutoWithdraw();
                
                if(autoWithdraw == 1){
                    //获取提现的最大金额 
                    BigDecimal maxAutoWithdraw = sysParams.getMaxAutoWithdraw();
                    //查询提现状态为0的记录金额小于maxAutoWithdraw的记录
                    List<WithdrawRecord> pendingWithdrawRecords = withdrawRecordMapper.selectPendingWithdrawRecordsByAmount(maxAutoWithdraw);
                    
                    if (!pendingWithdrawRecords.isEmpty()) {
                        log.info("找到 {} 条待处理的自动提现记录", pendingWithdrawRecords.size());                       
                        
                        for (WithdrawRecord record : pendingWithdrawRecords) {
                            try {
                                log.info("处理自动提现记录: ID={}, 用户={}, 金额={}", 
                                    record.getId(), record.getUsername(), record.getAmount());

                                ChainConfig chainConfig = chainConfigService.getConfig();
                                String fromAddress = chainConfig.getWithdrawAddress();
                                String encryptedPrivateKey = chainConfig.getWithdrawPrivateKey();
                                if (fromAddress == null || fromAddress.isEmpty() || encryptedPrivateKey == null || encryptedPrivateKey.isEmpty()) {
                                    throw new RuntimeException("链端提现地址或私钥未配置");

                                }
                                // 校验余额（链端余额已在工具类校验）
                                String toAddress = record.getAddress();
                                if (toAddress == null || toAddress.isEmpty()) {
                                    throw new RuntimeException("提现接收地址为空");
                                }
                                // 发起USDT链上转账
                                String txHash = com.admin.utils.Web3TransferUtil.transferUsdt(fromAddress, encryptedPrivateKey, toAddress, record.getRealAmount());
                                // 写入txHash
                                record.setTxHash(txHash);
                                record.setStatus(1); // 设置为已通过状态
                                record.setRemark("链上转账成功，txHash:" + txHash);
                                record.setUpdateTime(LocalDateTime.now());
                                boolean updateResult = withdrawRecordService.updateById(record);
                                if (!updateResult) {
                                    throw new RuntimeException("更新提现记录失败");
                                }
                                // 从冻结余额中扣除
                                boolean deductResult = withdrawRecordMapper.updateUserFrozenBalance(record.getUserId(), record.getAmount());
                                if (!deductResult) {
                                    throw new RuntimeException("扣除冻结余额失败");
                                }
                                
                                log.info("自动提现处理成功: ID={}, txHash={}", record.getId(), txHash);
                            } catch (Exception e) {
                                log.error("处理自动提现记录失败: ID={}, 错误={}", record.getId(), e.getMessage(), e);
                                // 更新记录状态为失败
//                                record.setStatus(2); // 设置为拒绝状态
//                                record.setRemark("自动提现处理失败: " + e.getMessage());
//                                record.setUpdateTime(LocalDateTime.now());
//                                withdrawRecordService.updateById(record);
                            }
                        }
                    } else {
                        log.debug("没有找到符合条件的自动提现记录");
                    }
                    
                    // 休眠1秒后继续检查
                    if (pendingWithdrawRecords.isEmpty()) {
                        Thread.sleep(1000);
                    }
                } else {
                    // 如果自动提现未启用，休眠5秒后继续检查
                    Thread.sleep(5000);
                }
                
            } catch (InterruptedException e) {
                log.info("小笔提现监控任务被中断");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("小笔提现监控任务执行异常", e);
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    } 

} 