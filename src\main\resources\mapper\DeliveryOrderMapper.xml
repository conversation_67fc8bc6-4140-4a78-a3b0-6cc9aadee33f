<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.DeliveryOrderMapper">

    <resultMap id="DeliveryOrderVOMap" type="com.admin.vo.DeliveryOrderVO">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="leaderId" column="leader_id"/>
        <result property="symbol" column="symbol"/>
        <result property="marginAmount" column="margin_amount"/>
        <result property="positionAmount" column="position_amount"/>
        <result property="lever" column="lever"/>
        <result property="direction" column="direction"/>
        <result property="takeProfit" column="take_profit"/>
        <result property="stopLoss" column="stop_loss"/>
        <result property="openPrice" column="open_price"/>
        <result property="closePrice" column="close_price"/>
        <result property="openTime" column="open_time"/>
        <result property="closeTime" column="close_time"/>
        <result property="status" column="status"/>
        <result property="profit" column="profit"/>
        <result property="rebateStatus" column="rebate_status"/>
        <result property="profitStatus" column="profit_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isSettlement" column="is_settlement"/>
        <!-- 用户信息 -->
        <result property="userAvatar" column="user_avatar"/>
        <result property="userEmail" column="user_email"/>
        <result property="userNo" column="user_no"/>
        <!-- 带单员信息 -->
        <result property="leaderAvatar" column="leader_avatar"/>
        <result property="leaderEmail" column="leader_email"/>
        <result property="leaderNo" column="leader_no"/>
    </resultMap>

    <select id="selectPageVO" resultMap="DeliveryOrderVOMap">
        SELECT d.*, 
            u.avatar AS user_avatar, u.email AS user_email, u.user_no AS user_no,
            l.avatar AS leader_avatar, l.email AS leader_email, l.user_no AS leader_no
        FROM delivery_order d
        LEFT JOIN front_user u ON d.user_id = u.id
        LEFT JOIN front_user l ON d.leader_id = l.id
        <where>
            <if test="params.symbol != null and params.symbol != ''">
                AND d.symbol = #{params.symbol}
            </if>
            <if test="params.userNo != null and params.userNo != ''">
                AND u.user_no = #{params.userNo}
            </if>
            <if test="params.leaderNo != null and params.leaderNo != ''">
                AND l.user_no = #{params.leaderNo}
            </if>
            <if test="params.rebateStatus != null and params.rebateStatus != ''">
                AND d.rebate_status = #{params.rebateStatus}
            </if>
            <if test="params.status != null and params.status != ''">
                AND d.status = #{params.status}
            </if>
            <if test="params.profitStatus != null and params.profitStatus != ''">
                AND d.profit_status = #{params.profitStatus}
            </if>
            <if test="params.direction != null and params.direction != ''">
                AND d.direction = #{params.direction}
            </if>
            <if test="params.isSettlement != null and params.isSettlement != ''">
                AND d.is_settlement = #{params.isSettlement}
            </if>
        </where>
        ORDER BY d.id DESC
        <!-- 分页由Mybatis-Plus自动处理 -->
    </select>

    <select id="selectDetailVO" resultMap="DeliveryOrderVOMap">
        SELECT d.*,
            u.avatar AS user_avatar, u.email AS user_email, u.user_no AS user_no,
            l.avatar AS leader_avatar, l.email AS leader_email, l.user_no AS leader_no
        FROM delivery_order d
        LEFT JOIN front_user u ON d.user_id = u.id
        LEFT JOIN front_user l ON d.leader_id = l.id
        WHERE d.id = #{id}
    </select>

    <!-- 查询需要补仓的订单 -->
    <select id="selectReplenishOrders" resultType="com.admin.entity.DeliveryOrder">
        SELECT * FROM delivery_order
        WHERE status = 2
        AND rebate_status = 1
        AND profit_status = 2
        <!-- AND is_settlement = 1 -->
        ORDER BY id ASC
    </select>

    <!-- 更新订单的返利状态 -->
    <update id="updateRebateStatus">
        UPDATE delivery_order
        SET rebate_status = #{rebateStatus}, update_time = NOW()
        WHERE id = #{orderId}
    </update>

</mapper>