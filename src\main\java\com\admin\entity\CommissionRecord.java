package com.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("commission_record")
public class CommissionRecord {
    private Long id;
    private Long userId;
    private String username;
    private String phone;
    private Integer commissionType;
    private BigDecimal commissionAmount;
    private Integer releaseStatus;
    private LocalDateTime releaseTime;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 