package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("chain_config")
public class ChainConfig {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String collectAddress;
    private BigDecimal collectBalance;
    private String bnbAddress;
    private BigDecimal bnbBalance;
    private String bnbPrivateKey;
    private String withdrawAddress;
    private String withdrawPrivateKey;
    private BigDecimal withdrawBnbBalance;
    private BigDecimal withdrawUsdtBalance;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 