package com.admin.mapper;

import com.admin.entity.WithdrawRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface WithdrawRecordMapper extends BaseMapper<WithdrawRecord> {
    
    /**
     * 分页查询提现记录
     */
    IPage<WithdrawRecord> selectWithdrawRecordList(Page<WithdrawRecord> page, @Param("params") Map<String, Object> params);
    
    /**
     * 查询提现统计信息
     */
    Map<String, Object> selectWithdrawStatistics();
    
    /**
     * 更新用户账户余额(拒绝时返还)
     */
    boolean updateUserBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
    
    /**
     * 更新用户冻结余额(通过时扣除)
     */
    boolean updateUserFrozenBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
    
    /**
     * 查询提现记录(导出用)
     */
    List<WithdrawRecord> selectWithdrawRecordForExport(@Param("username") String username, 
        @Param("status") String status, 
        @Param("startDate") String startDate, 
        @Param("endDate") String endDate);
    
    /**
     * 查询状态为0且金额小于指定金额的提现记录
     */
    List<WithdrawRecord> selectPendingWithdrawRecordsByAmount(@Param("maxAmount") BigDecimal maxAmount);
} 