# 用户自动激活功能实现

## 功能概述

在用户链上充值后，系统会自动检查用户的总充值金额，如果达到1000 USDT或以上，则自动激活用户账户。

## 实现逻辑

### 1. 触发时机
- 在 `ChainRechargeListenerService.processRecharge()` 方法中
- 累加资金账户充值币结束后立即执行

### 2. 执行流程

```
链上充值 → 写入充值记录 → 写入交易明细 → 累加资金账户 → 检查并激活用户
```

### 3. 详细步骤

1. **检查用户激活状态**
   ```sql
   SELECT is_activated FROM front_user WHERE id = #{userId}
   ```
   - 如果已激活 (is_activated = 1)，则跳过后续步骤

2. **查询用户总充值金额**
   ```sql
   SELECT COALESCE(SUM(amount), 0) FROM recharge_record 
   WHERE user_id = #{userId} AND audit_status = 1
   ```
   - 只统计已审核通过的充值记录 (audit_status = 1)
   - 使用 COALESCE 确保没有记录时返回 0

3. **激活用户账户**
   ```sql
   UPDATE front_user SET is_activated = 1 
   WHERE id = #{userId} AND is_activated = 0
   ```
   - 只有未激活的用户才会被更新
   - 防止重复激活

## 代码实现

### FrontUserMapper 新增方法

```java
/**
 * 查询用户总充值金额
 */
@Select("SELECT COALESCE(SUM(amount), 0) FROM recharge_record WHERE user_id = #{userId} AND audit_status = 1")
BigDecimal getTotalRechargeAmount(@Param("userId") Long userId);

/**
 * 检查用户是否已激活
 */
@Select("SELECT is_activated FROM front_user WHERE id = #{userId}")
Integer checkUserActivationStatus(@Param("userId") Long userId);

/**
 * 激活用户账户（仅当未激活时）
 */
@Update("UPDATE front_user SET is_activated = 1 WHERE id = #{userId} AND is_activated = 0")
int activateUserAccount(@Param("userId") Long userId);
```

### ChainRechargeListenerService 新增方法

```java
/**
 * 检查用户总充值金额，如果达到1000则激活账户
 */
private void checkAndActivateUser(Long userId) {
    try {
        // 1. 检查用户是否已激活
        Integer isActivated = frontUserMapper.checkUserActivationStatus(userId);
        if (isActivated != null && isActivated == 1) {
            log.debug("用户ID: {} 已经激活，无需重复激活", userId);
            return;
        }

        // 2. 查询用户总充值金额
        BigDecimal totalRecharge = frontUserMapper.getTotalRechargeAmount(userId);
        log.info("用户ID: {} 总充值金额: {}", userId, totalRecharge);

        // 3. 如果总充值金额大于等于1000，则激活用户
        if (totalRecharge.compareTo(new BigDecimal("1000")) >= 0) {
            int updateCount = frontUserMapper.activateUserAccount(userId);
            if (updateCount > 0) {
                log.info("用户ID: {} 充值金额达到 {} USDT，账户已自动激活", userId, totalRecharge);
            } else {
                log.warn("用户ID: {} 激活失败，可能已经激活或用户不存在", userId);
            }
        } else {
            log.debug("用户ID: {} 充值金额 {} USDT 未达到激活条件(1000 USDT)", userId, totalRecharge);
        }
    } catch (Exception e) {
        log.error("检查并激活用户失败，用户ID: {}", userId, e);
    }
}
```

## 数据库表结构

### front_user 表相关字段
- `id`: 用户ID (主键)
- `is_activated`: 是否激活 (0:未激活, 1:已激活)

### recharge_record 表相关字段
- `user_id`: 用户ID
- `amount`: 充值金额
- `audit_status`: 审核状态 (0:待审核, 1:已通过, 2:已拒绝)

## 日志记录

### 正常激活
```
用户ID: 123 总充值金额: 1500.00
用户ID: 123 充值金额达到 1500.00 USDT，账户已自动激活
```

### 未达到激活条件
```
用户ID: 123 总充值金额: 500.00
用户ID: 123 充值金额 500.00 USDT 未达到激活条件(1000 USDT)
```

### 已激活用户
```
用户ID: 123 已经激活，无需重复激活
```

### 异常情况
```
检查并激活用户失败，用户ID: 123
```

## 安全特性

1. **防重复激活**: 使用 `AND is_activated = 0` 条件确保只有未激活用户才会被更新
2. **事务安全**: 在现有事务中执行，确保数据一致性
3. **异常处理**: 包含完整的异常捕获和日志记录
4. **状态验证**: 激活前先检查当前状态，避免不必要的数据库操作

## 测试场景

1. **新用户首次充值1000+**: 应该被激活
2. **新用户多次充值累计1000+**: 应该在达到1000时被激活
3. **已激活用户继续充值**: 不应该重复激活
4. **充值金额不足1000**: 不应该被激活
5. **数据库异常**: 应该记录错误日志但不影响主流程

## 注意事项

- 激活条件: 总充值金额 >= 1000 USDT
- 只统计审核通过的充值记录 (audit_status = 1)
- 激活是不可逆的操作
- 系统会记录详细的操作日志用于审计
