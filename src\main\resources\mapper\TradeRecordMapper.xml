<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.TradeRecordMapper">

    <resultMap id="BaseResultMap" type="com.admin.entity.TradeRecord">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="trade_type" property="tradeType" />
        <result column="amount" property="amount" />
        <result column="account_type" property="accountType" />
        <result column="remark" property="remark" />
        <result column="email" property="email" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <insert id="insertTradeRecord">
        INSERT INTO trade_record (
            user_id, username, trade_type, amount, account_type, remark, create_time, update_time
        ) VALUES (
            #{userId}, #{username}, #{tradeType}, #{amount}, #{accountType}, #{remark}, NOW(), NOW()
        )
    </insert>
    <select id="selectTradeRecordList" resultMap="BaseResultMap">
        SELECT t.*, u.email
        FROM trade_record t
        LEFT JOIN front_user u ON t.user_id = u.id
        <where>
            <if test="params.username != null and params.username != ''">
                AND t.username = #{params.username}
            </if>
            <if test="params.email != null and params.email != ''">
                AND u.email = #{params.email}
            </if>
            <if test="params.tradeType != null and params.tradeType != ''">
                AND t.trade_type = #{params.tradeType}
            </if>
            <if test="params.accountType != null and params.accountType != ''">
                AND t.account_type = #{params.accountType}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper> 