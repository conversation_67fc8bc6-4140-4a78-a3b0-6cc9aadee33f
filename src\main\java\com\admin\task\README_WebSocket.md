# Binance WebSocket Price Sync Task

## 概述

`BinanceWebSocketPriceSyncTask` 是一个使用WebSocket实时获取币安24小时ticker数据的任务类，用于替代原有的REST API轮询方式。

## 重要说明

**此实现已完成并可以直接使用！** 它使用Java-WebSocket库（兼容Java 8）来建立WebSocket连接，获取的数据格式与原有REST API完全一致。

### ✅ 最新修复 (2025-07-28)
- **字段名映射**: WebSocket字段名已正确映射为REST API字段名
- **缺失字段补全**: 添加了WebSocket中没有的字段（prevClosePrice, bidPrice, bidQty, askPrice, askQty）
- **数据格式一致**: 现在输出的JSON格式与REST API完全相同

## 功能特点

1. **实时数据**: 使用WebSocket连接，实时接收币安24小时ticker数据
2. **自动重连**: 连接断开时自动重连，确保数据连续性
3. **数据缓存**: 将接收到的数据缓存在内存中，定期写入Redis
4. **兼容性**: 返回的数据结构与原有REST API完全一致
5. **资源管理**: 应用关闭时自动清理WebSocket连接

## 与REST API的对比

### 原有REST API方式 (BinancePriceSyncTask)
- 每3秒轮询一次API
- 需要为每个交易对发送单独的HTTP请求
- 可能存在请求限制和延迟

### 新WebSocket方式 (BinanceWebSocketPriceSyncTask)
- 实时推送数据，延迟更低
- 单个连接处理所有交易对
- 减少服务器负载和网络请求

## 数据格式

WebSocket接收到的数据会被转换为与REST API相同的格式：

```json
{
  "symbol": "BTCUSDT",
  "priceChange": "1000.00",
  "priceChangePercent": "2.50",
  "weightedAvgPrice": "40500.00",
  "lastPrice": "41000.00",
  "lastQty": "0.1",
  "openPrice": "40000.00",
  "highPrice": "42000.00",
  "lowPrice": "39500.00",
  "volume": "1000.00",
  "quoteVolume": "40500000.00",
  "openTime": 1672515780000,
  "closeTime": 1672602180000,
  "firstId": "100",
  "lastId": "200",
  "count": "101"
}
```

## WebSocket连接详情

- **端点**: `wss://stream.binance.com:9443/stream`
- **流格式**: `{symbol}@ticker` (例如: `btcusdt@ticker`)
- **连接方式**: Combined Stream (多个流合并)
- **消息格式**: `{"stream": "btcusdt@ticker", "data": {...}}`

## 配置要求

无需额外配置，使用与原有任务相同的数据库和Redis配置。

## 依赖要求

项目已自动添加了必要的依赖：
```xml
<dependency>
    <groupId>org.java-websocket</groupId>
    <artifactId>Java-WebSocket</artifactId>
    <version>1.5.3</version>
</dependency>
```

## 使用方法

1. **停用原有任务**：在 `BinancePriceSyncTask.java` 中注释掉 `@Scheduled` 注解
   ```java
   // @Scheduled(fixedDelay = 3000)  // 注释掉这行
   public void syncBinanceSpotTickers() {
   ```

2. **启动应用**：`BinanceWebSocketPriceSyncTask` 会自动初始化并连接WebSocket

3. **数据存储**：数据会自动写入Redis，键格式为 `binance:ticker:{symbol}`

## 日志监控

- 连接建立: `币安WebSocket连接已建立`
- 连接断开: `币安WebSocket连接关闭`
- 重连尝试: `尝试重新连接币安WebSocket...`
- 数据更新: `更新ticker缓存: {symbol}`

## 故障处理

1. **连接失败**: 自动重连，5秒后重试
2. **数据解析错误**: 记录错误日志，继续处理其他消息
3. **网络中断**: 自动检测并重新建立连接

## 性能优势

- 减少HTTP请求数量（从每3秒N个请求减少到1个持久连接）
- 降低延迟（实时推送 vs 轮询）
- 减少服务器负载
- 提高数据实时性
