<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.CommissionRecordMapper">
    
    <insert id="insertBatch">
        INSERT INTO commission_record (
            user_id, username, phone, commission_type, commission_amount,
            release_status, release_time, remark, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userId}, #{item.username}, #{item.phone},
                #{item.commissionType}, #{item.commissionAmount},
                #{item.releaseStatus}, #{item.releaseTime},
                #{item.remark}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>
    
    <insert id="insertCommission">
        INSERT INTO commission_record
        (user_id, username, phone, commission_type, commission_amount, release_status, release_time, remark, create_time, update_time)
        VALUES
        (#{userId}, #{username}, #{phone}, #{commissionType}, #{commissionAmount}, 1, NOW(), #{remark}, NOW(), NOW())
    </insert>
    
</mapper> 