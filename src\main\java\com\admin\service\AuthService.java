package com.admin.service;

import com.admin.dto.LoginDTO;
import com.admin.dto.ResetPasswordDTO;
import com.admin.entity.SysUser;
import com.admin.vo.CaptchaVO;

public interface AuthService {
    /**
     * 生成验证码
     */
    CaptchaVO generateCaptcha();

    /**
     * 用户登录
     */
    String login(LoginDTO loginDTO);

    void sendResetCode(String email);
    
    void resetPassword(ResetPasswordDTO resetPasswordDTO);

    /**
     * 获取当前登录用户信息
     */
    SysUser getUserInfo();

    void updatePassword(SysUser user);
} 