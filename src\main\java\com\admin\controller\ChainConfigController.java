package com.admin.controller;

import com.admin.common.Result;
import com.admin.entity.ChainConfig;
import com.admin.service.ChainConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/chain/config")
public class ChainConfigController {
    @Autowired
    private ChainConfigService chainConfigService;

    /**
     * 查询链端参数（id=1）
     */
    @GetMapping
    public Result getConfig() {
        ChainConfig config = chainConfigService.getConfig();
        return Result.ok().data("data", config);
    }

    /**
     * 修改链端参数（id=1）
     */
    @PutMapping
    public Result updateConfig(@RequestBody ChainConfig config) {
        config.setId(1L); // 强制只允许修改id=1
        int result = chainConfigService.updateConfig(config);
        if (result > 0) {
            return Result.ok();
        } else {
            return Result.error("修改失败");
        }
    }

    /**
     * 解密私钥（新版，接收密文）
     */
    @PostMapping("/decrypt")
    public Result decryptPrivateKey(@RequestBody Map<String, Object> params) {
        Object encrypted = params.get("encrypted");
        if (encrypted == null) {
            return Result.error("无密文");
        }
        String plain;
        try {
            plain = com.admin.utils.AESUtil.decrypt(encrypted.toString());
        } catch (Exception e) {
            return Result.error("解密失败");
        }
        return Result.ok().data("data", plain);
    }
} 