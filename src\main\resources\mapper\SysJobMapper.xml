<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.SysJobMapper">

    <select id="selectJobList" resultType="com.admin.entity.SysJob">
        select * from sys_job
        <where>
            <if test="jobName != null and jobName != ''">
                AND job_name like concat('%', #{jobName}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectJobById" resultType="com.admin.entity.SysJob">
        select * from sys_job where id = #{jobId}
    </select>
    
    <insert id="insertJob" useGeneratedKeys="true" keyProperty="id">
        insert into sys_job(
            job_name, job_type, invoke_target, cron_expression,
            misfire_policy, concurrent, status, remark,
            create_by, create_time
        )values(
            #{jobName}, #{jobType}, #{invokeTarget}, #{cronExpression},
            #{misfirePolicy}, #{concurrent}, #{status}, #{remark},
            #{createBy}, sysdate()
        )
    </insert>
    
    <update id="updateJob">
        update sys_job
        <set>
            <if test="jobName != null">job_name = #{jobName},</if>
            <if test="jobType != null">job_type = #{jobType},</if>
            <if test="invokeTarget != null">invoke_target = #{invokeTarget},</if>
            <if test="cronExpression != null">cron_expression = #{cronExpression},</if>
            <if test="misfirePolicy != null">misfire_policy = #{misfirePolicy},</if>
            <if test="concurrent != null">concurrent = #{concurrent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>
    
    <delete id="deleteJobById">
        delete from sys_job where id = #{jobId}
    </delete>
    
</mapper> 