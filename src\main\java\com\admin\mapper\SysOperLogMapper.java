package com.admin.mapper;

import com.admin.entity.SysOperLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Map;

@Mapper
public interface SysOperLogMapper extends BaseMapper<SysOperLog> {
    // 分页查询操作日志
    IPage<SysOperLog> selectOperLogPage(Page<SysOperLog> page, @Param("params") Map<String, Object> params);
} 