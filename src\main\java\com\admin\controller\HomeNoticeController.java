package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.HomeNotice;
import com.admin.service.HomeNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/home/<USER>")
public class HomeNoticeController {

    @Autowired
    private HomeNoticeService homeNoticeService;
    @Log(title = "首页公告", operType = "查询")
    @GetMapping("/info")
    public R info() {
        try {
            HomeNotice notice = homeNoticeService.getHomeNotice();
            return R.ok().put("data", notice);
        } catch (Exception e) {
            log.error("获取首页通知失败", e);
            return R.error("获取失败：" + e.getMessage());
        }
    }
    @Log(title = "首页公告", operType = "修改")
    @PostMapping("/save")
    public R save(@RequestBody HomeNotice notice) {
        try {
            boolean success = homeNoticeService.saveHomeNotice(notice);
            return success ? R.ok() : R.error("保存失败");
        } catch (Exception e) {
            log.error("保存首页通知失败", e);
            return R.error("保存失败：" + e.getMessage());
        }
    }
    @Log(title = "首页公告", operType = "更新")
    @PutMapping("/status/{id}")
    public R updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            boolean success = homeNoticeService.updateStatus(id, status);
            if (success) {
                HomeNotice notice = homeNoticeService.getById(id);
                return R.ok().put("data", notice);
            }
            return R.error("更新状态失败");
        } catch (Exception e) {
            log.error("更新首页通知状态失败", e);
            return R.error("更新失败：" + e.getMessage());
        }
    }
} 