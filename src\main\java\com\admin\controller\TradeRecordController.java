package com.admin.controller;

import com.admin.entity.TradeRecord;
import com.admin.service.TradeRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/tradeRecord")
public class TradeRecordController {
    @Autowired
    private TradeRecordService tradeRecordService;

    /**
     * 分页多条件查询交易明细
     * @param params pageNum, pageSize, username, email, tradeType, accountType
     */
    @GetMapping("/list")
    public Map<String, Object> list(@RequestParam Map<String, Object> params) {
        int pageNum = params.get("pageNum") == null ? 1 : Integer.parseInt(params.get("pageNum").toString());
        int pageSize = params.get("pageSize") == null ? 10 : Integer.parseInt(params.get("pageSize").toString());
        Page<TradeRecord> page = new Page<>(pageNum, pageSize);
        IPage<TradeRecord> result = tradeRecordService.pageTradeRecord(page, params);
        Map<String, Object> resp = new HashMap<>();
        resp.put("total", result.getTotal());
        resp.put("records", result.getRecords());
        return resp;
    }
} 