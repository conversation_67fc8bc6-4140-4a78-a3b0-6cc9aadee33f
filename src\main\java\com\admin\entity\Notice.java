package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("notice")
public class Notice {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String title;
    private String content;
    private Integer noticeType;  // 公告类型(1:系统公告,2:活动公告,3:维护公告)
    private Integer sort;
    private Integer status;  // 状态(0:禁用,1:正常)
    private Integer isTop;  // 是否置顶(0:否,1:是)
    private LocalDateTime publishTime;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
} 