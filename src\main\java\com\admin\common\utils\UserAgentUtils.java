package com.admin.common.utils;

import eu.bitwalker.useragentutils.UserAgent;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

public class UserAgentUtils {
    
    public static String getBrowser() {
        UserAgent userAgent = getUserAgent();
        if (userAgent != null && userAgent.getBrowser() != null) {
            return userAgent.getBrowser().getName();
        }
        return "Unknown";
    }
    
    public static String getOperatingSystem() {
        UserAgent userAgent = getUserAgent();
        if (userAgent != null && userAgent.getOperatingSystem() != null) {
            return userAgent.getOperatingSystem().getName();
        }
        return "Unknown";
    }
    
    public static String getLoginLocation() {
        String ip = IpUtils.getIpAddr();
        return "内网IP".equals(ip) ? "内网IP" : "未知位置"; // 这里可以接入第三方IP地址库获取具体位置
    }
    
    private static UserAgent getUserAgent() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String userAgentString = request.getHeader("User-Agent");
            return UserAgent.parseUserAgentString(userAgentString);
        }
        return null;
    }
} 