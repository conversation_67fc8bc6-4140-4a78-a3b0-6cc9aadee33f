package com.admin.mapper;

import com.admin.entity.ExchangePairInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交易对管理Mapper接口
 */
@Mapper
public interface ExchangePairMapper {
    /**
     * 查询交易对列表（分页）
     */
    IPage<ExchangePairInfo> getExchangePairPage(Page<ExchangePairInfo> page, @Param("exchangeName") String exchangeName, @Param("pairName") String pairName, @Param("specialProcessing") Integer specialProcessing, @Param("isEnabled") Integer isEnabled);

    /**
     * 查询交易对列表
     */
    List<ExchangePairInfo> selectExchangePairList(ExchangePairInfo exchangePair);

    /**
     * 查询交易对信息
     */
    ExchangePairInfo selectExchangePairById(Long id);

    /**
     * 新增交易对
     */
    int insertExchangePair(ExchangePairInfo exchangePair);

    /**
     * 修改交易对
     */
    int updateExchangePair(ExchangePairInfo exchangePair);

    /**
     * 删除交易对
     */
    int deleteExchangePairByIds(Long[] ids);

    /**
     * 查询所有启用的交易对
     */
    List<String> getEnabledPairNames();

    /**
     * 查询所有启用的交易对名称
     */
    List<String> selectEnabledPairNames();
} 