package com.admin.controller;

import com.admin.service.FuturesOptionOrderService;
import com.admin.vo.FuturesOptionOrderVO;
import com.admin.common.utils.R;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/futuresOptionOrder")
public class FuturesOptionOrderController {
    @Resource
    private FuturesOptionOrderService futuresOptionOrderService;

    @GetMapping("/list")
    public R list(@RequestParam(defaultValue = "1") int pageNum,
                  @RequestParam(defaultValue = "10") int pageSize,
                  @RequestParam(required = false) String symbol,
                  @RequestParam(required = false) String userNo,
                  @RequestParam(required = false) Integer isSettlement,
                  @RequestParam(required = false) Integer status,
                  @RequestParam(required = false) Integer profitStatus,
                  @RequestParam(required = false) Integer direction) {
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        if (symbol != null && !symbol.isEmpty()) params.put("symbol", symbol);
        if (userNo != null && !userNo.isEmpty()) params.put("userNo", userNo);
        if (isSettlement != null) params.put("isSettlement", isSettlement);
        if (status != null) params.put("status", status);
        if (profitStatus != null) params.put("profitStatus", profitStatus);
        if (direction != null) params.put("direction", direction);
        List<FuturesOptionOrderVO> list = futuresOptionOrderService.selectPageVO(params);
        return R.ok().put("data", list);
    }

    @GetMapping("/detail")
    public R detail(@RequestParam Long id) {
        FuturesOptionOrderVO vo = futuresOptionOrderService.selectDetailVO(id);
        return R.ok().put("data", vo);
    }
} 