package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.SysLoginLog;
import com.admin.service.SysLoginLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.springframework.format.annotation.DateTimeFormat;

@RestController
@RequestMapping("/loginlog")
public class SysLoginLogController {

    @Autowired
    private SysLoginLogService loginLogService;
    @Log(title = "登录日志", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam(required = false) String username,
                 @RequestParam(required = false) String ip,
                 @RequestParam(required = false) Integer status,
                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime startDate,
                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime endDate,
                 @RequestParam(defaultValue = "1") Integer page,
                 @RequestParam(defaultValue = "10") Integer limit) {
        
        IPage<SysLoginLog> pageData = loginLogService.getLoginLogList(
            username, ip, status, startDate, endDate, page, limit);
            
        return R.ok()
                .put("data", pageData.getRecords())
                .put("total", pageData.getTotal());
    }
    @Log(title = "登录日志", operType = "清空")
    @DeleteMapping("/clean")
    public R clean() {
        return loginLogService.cleanLoginLog() ? R.ok() : R.error("清空失败");
    }
} 