package com.admin.service;

import com.admin.entity.FrontUser;
import com.admin.model.query.FrontUserQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface FrontUserService extends IService<FrontUser> {
    
    IPage<FrontUser> getUserList(Page<FrontUser> page, FrontUserQuery query);
    
    FrontUser getUserDetail(Long id);
    
    boolean updateUserStatus(Long id, Integer status);

    boolean updateUserActivatedStatus(Long id, Integer isActivated);

    boolean resetPassword(Long id);
    
    boolean recharge(Long id, BigDecimal amount, String remark);
    
    List<Map<String, Object>> getTeamTopology();
    
    List<Map<String, Object>> getTeamTopologyByEmail(String email);
    
    /**
     * 重置所有用户的团队今日统计
     * @return 重置的用户数量
     */
    int resetTeamTodayCount();
    
    

 

    /**
     * 更新用户可用余额
     * @param userId 用户ID
     * @param amount 要增加的金额
     * @return 更新是否成功
     */
    boolean updateAvailableBalance(Long userId, BigDecimal amount);



    /**
     * 检查用户是否有推荐的下级用户
     * @param shareCode 用户的分享码
     * @return true:有下级用户 false:没有下级用户
     */
    boolean hasReferrals(String shareCode);

    /**
     * 删除用户
     * @param userId 用户ID
     * @return true:删除成功 false:删除失败
     */
    boolean deleteUser(Long userId);
} 