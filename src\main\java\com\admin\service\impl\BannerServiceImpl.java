package com.admin.service.impl;

import com.admin.entity.Banner;
import com.admin.mapper.BannerMapper;
import com.admin.service.BannerService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;

@Service
public class BannerServiceImpl extends ServiceImpl<BannerMapper, Banner> implements BannerService {

    @Override
    public IPage<Banner> getBannerList(String title, Integer status, Integer page, Integer limit) {
        LambdaQueryWrapper<Banner> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(title), Banner::getTitle, title)
               .eq(status != null, Banner::getStatus, status)
               .orderByAsc(Banner::getSort)
               .orderByDesc(Banner::getCreateTime);
               
        return page(new Page<>(page, limit), wrapper);
    }

    @Override
    public boolean addBanner(Banner banner) {
        banner.setCreateTime(LocalDateTime.now());
        banner.setCreateBy("admin"); // 这里应该从登录用户中获取
        return save(banner);
    }

    @Override
    public boolean updateBanner(Banner banner) {
        banner.setUpdateTime(LocalDateTime.now());
        banner.setUpdateBy("admin"); // 这里应该从登录用户中获取
        return updateById(banner);
    }

    @Override
    public boolean toggleStatus(Long id, Integer status) {
        Banner banner = new Banner();
        banner.setId(id);
        banner.setStatus(status);
        banner.setUpdateTime(LocalDateTime.now());
        banner.setUpdateBy("admin"); // 这里应该从登录用户中获取
        return updateById(banner);
    }

    @Override
    public boolean deleteBanner(Long id) {
        return removeById(id);
    }
} 