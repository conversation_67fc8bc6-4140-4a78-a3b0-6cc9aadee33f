package com.admin.mapper;

import com.admin.entity.FuturesOptionOrder;
import com.admin.vo.FuturesOptionOrderVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

public interface FuturesOptionOrderMapper extends BaseMapper<FuturesOptionOrder> {
    IPage<Map<String, Object>> pageWithUser(Page<Map<String, Object>> page,
                                            @Param("symbol") String symbol,
                                            @Param("winOrLose") String winOrLose,
                                            @Param("leverType") String leverType,
                                            @Param("positionStatus") String positionStatus,
                                            @Param("userNo") String userNo,
                                            @Param("username") String username,
                                            @Param("email") String email,
                                            @Param("status") String status,
                                            @Param("startTime") String startTime,
                                            @Param("endTime") String endTime);
    List<FuturesOptionOrderVO> selectPageVO(@Param("params") Map<String, Object> params);
    FuturesOptionOrderVO selectDetailVO(@Param("id") Long id);
} 