package com.admin.controller;

import com.admin.common.utils.R;
import com.admin.entity.CommissionRecord;
import com.admin.service.CommissionRecordService;
import com.admin.vo.CommissionStats;
import com.admin.vo.CommissionExportVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import com.admin.common.exception.BusinessException;
import java.math.BigDecimal;

@Slf4j
@RestController
@RequestMapping("/commission")
public class CommissionRecordController {

    @Autowired
    private CommissionRecordService commissionRecordService;

    @GetMapping("/list")
    public R list(@RequestParam(required = false) String keyword,
                 @RequestParam(required = false) String commissionType,
                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
                 @RequestParam(defaultValue = "1") Integer page,
                 @RequestParam(defaultValue = "10") Integer limit) {
        
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        
        IPage<CommissionRecord> pageData = commissionRecordService.getCommissionList(
            keyword, commissionType, startDateTime, endDateTime, page, limit);
            
        return R.ok()
                .put("data", pageData.getRecords())
                .put("total", pageData.getTotal());
    }

    /**
     * 获取筛选条件下的总金额
     */
    @GetMapping("/total")
    public R getFilteredTotal(@RequestParam(required = false) String keyword,
                            @RequestParam(required = false) String commissionType,
                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
            LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
            
            BigDecimal total = commissionRecordService.calculateFilteredTotal(
                keyword, commissionType, startDateTime, endDateTime);
                
            return R.ok().put("total", total);
        } catch (Exception e) {
            log.error("计算总金额失败", e);
            return R.error("计算总金额失败");
        }
    }

    @GetMapping("/stats")
    public R getStats() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime todayStart = LocalDate.now().atStartOfDay();
            LocalDateTime weekStart = todayStart.minusDays(7);
            LocalDateTime monthStart = todayStart.withDayOfMonth(1);
            
            // 获取总的统计数据 - 统计所有已发放的记录
            CommissionStats totalStats = commissionRecordService.getCommissionStats(null, now);
            
            // 获取其他时间段的统计
            CommissionStats todayStats = commissionRecordService.getCommissionStats(todayStart, now);
            CommissionStats weekStats = commissionRecordService.getCommissionStats(weekStart, now);
            CommissionStats monthStats = commissionRecordService.getCommissionStats(monthStart, now);

            return R.ok()
                    .put("total", totalStats)
                    .put("today", todayStats)
                    .put("week", weekStats)
                    .put("month", monthStats);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return R.error("获取统计数据失败");
        }
    }

    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) String keyword,
                       @RequestParam(required = false) String commissionType,
                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime startDate,
                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime endDate) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("赠送记录", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            
            // 获取导出数据
            List<CommissionRecord> list = commissionRecordService.getExportList(keyword, commissionType, startDate, endDate);
            
            // 转换并设置序号
            List<CommissionExportVO> exportList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                CommissionExportVO vo = convertToExportVO(list.get(i));
                vo.setIndex(i + 1); // 设置序号，从1开始
                exportList.add(vo);
            }
            
            // 导出数据
            EasyExcel.write(response.getOutputStream(), CommissionExportVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("赠送记录")
                    .doWrite(exportList);
                    
        } catch (Exception e) {
            log.error("导出赠送记录失败", e);
            throw new BusinessException("导出失败");
        }
    }
    
    private CommissionExportVO convertToExportVO(CommissionRecord record) {
        CommissionExportVO vo = new CommissionExportVO();
        vo.setUsername(record.getUsername());
        vo.setPhone(record.getPhone());
        vo.setCommissionType(record.getCommissionType());
        vo.setCommissionAmount(record.getCommissionAmount());
        vo.setReleaseTime(record.getReleaseTime());
        return vo;
    }
} 