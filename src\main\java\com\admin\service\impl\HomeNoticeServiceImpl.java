package com.admin.service.impl;

import com.admin.entity.HomeNotice;
import com.admin.mapper.HomeNoticeMapper;
import com.admin.service.HomeNoticeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;

@Service
public class HomeNoticeServiceImpl extends ServiceImpl<HomeNoticeMapper, HomeNotice> implements HomeNoticeService {

    @Override
    public HomeNotice getHomeNotice() {
        // 获取最新的一条通知
        return getOne(new LambdaQueryWrapper<HomeNotice>()
                .orderByDesc(HomeNotice::getCreateTime)
                .last("LIMIT 1"));
    }

    @Override
    public boolean saveHomeNotice(HomeNotice notice) {
        if (notice.getId() == null) {
            notice.setCreateTime(LocalDateTime.now());
            notice.setCreateBy("admin"); // 应该从登录用户中获取
        }
        notice.setUpdateTime(LocalDateTime.now());
        notice.setUpdateBy("admin"); // 应该从登录用户中获取
        return saveOrUpdate(notice);
    }

    @Override
    public boolean updateStatus(Long id, Integer status) {
        HomeNotice notice = new HomeNotice();
        notice.setId(id);
        notice.setStatus(status);
        notice.setUpdateTime(LocalDateTime.now());
        notice.setUpdateBy("admin"); // 应该从登录用户中获取
        return updateById(notice);
    }
} 