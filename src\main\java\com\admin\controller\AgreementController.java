package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.Agreement;
import com.admin.service.AgreementService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/agreement")
public class AgreementController {

    @Autowired
    private AgreementService agreementService;
    @Log(title = "条款列表", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam(required = false) String title,
                 @RequestParam(defaultValue = "1") Integer page,
                 @RequestParam(defaultValue = "10") Integer limit) {
        try {
            IPage<Agreement> pageData = agreementService.getAgreementList(title, page, limit);
            return R.ok()
                    .put("data", pageData.getRecords())
                    .put("total", pageData.getTotal());
        } catch (Exception e) {
            log.error("获取协议列表失败", e);
            return R.error("获取协议列表失败");
        }
    }
    @Log(title = "条款列表", operType = "新增")
    @PostMapping("/add")
    public R add(@RequestBody Agreement agreement) {
        try {
            if (agreementService.addAgreement(agreement)) {
                return R.ok();
            }
            return R.error("新增协议失败");
        } catch (Exception e) {
            log.error("新增协议失败", e);
            return R.error("新增协议失败");
        }
    }
    @Log(title = "条款列表", operType = "修改")
    @PutMapping("/update")
    public R update(@RequestBody Agreement agreement) {
        try {
            if (agreementService.updateAgreement(agreement)) {
                return R.ok();
            }
            return R.error("修改协议失败");
        } catch (Exception e) {
            log.error("修改协议失败", e);
            return R.error("修改协议失败");
        }
    }
    @Log(title = "条款列表", operType = "删除")
    @DeleteMapping("/delete/{id}")
    public R delete(@PathVariable Long id) {
        try {
            if (agreementService.deleteAgreement(id)) {
                return R.ok();
            }
            return R.error("删除协议失败");
        } catch (Exception e) {
            log.error("删除协议失败", e);
            return R.error("删除协议失败");
        }
    }
} 