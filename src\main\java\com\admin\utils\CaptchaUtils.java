package com.admin.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;

public class CaptchaUtils {
    
    public static String generateImageBase64(String code) {
        // 创建图片
        BufferedImage image = new BufferedImage(100, 40, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置背景色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, 100, 40);
        
        // 设置字体
        g.setFont(new Font("Arial", Font.BOLD, 30));
        
        // 添加干扰线
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            g.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
            g.drawLine(random.nextInt(100), random.nextInt(40), 
                      random.nextInt(100), random.nextInt(40));
        }
        
        // 绘制验证码
        g.setColor(Color.BLACK);
        for (int i = 0; i < code.length(); i++) {
            g.drawString(String.valueOf(code.charAt(i)), 20 * i + 10, 30);
        }
        
        // 转换成base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "png", baos);
        } catch (IOException e) {
            throw new RuntimeException("生成验证码图片失败", e);
        }
        
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(baos.toByteArray());
    }
} 