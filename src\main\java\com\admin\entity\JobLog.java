package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("sys_job_log")
public class JobLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long jobId;
    private String jobName;
    private String jobType;
    private LocalDateTime executionTime;
    private Integer executionDuration;
    private String executionResult;
    private String executionMessage;
    private LocalDateTime createTime;
} 