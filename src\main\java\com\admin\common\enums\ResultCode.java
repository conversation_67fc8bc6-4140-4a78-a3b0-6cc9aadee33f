package com.admin.common.enums;

public enum ResultCode {
    SUCCESS(0, "操作成功"),
    ERROR(500, "系统错误"),
    CAPTCHA_ERROR(1001, "验证码错误"),
    CAPTCHA_EXPIRED(1002, "验证码已过期"),
    LOGIN_ERROR(1003, "用户名或密码错误"),
    ACCOUNT_DISABLED(1004, "账号已被禁用"),
    EMAIL_NOT_FOUND(1005, "该邮箱未注册，请确认后重试"),
    RESET_CODE_ERROR(1006, "重置验证码错误或已过期");

    private final int code;
    private final String msg;

    ResultCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
} 