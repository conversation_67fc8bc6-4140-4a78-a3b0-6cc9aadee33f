package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.SysParams;
import com.admin.service.SysParamsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统参数配置
 */
@RestController
@RequestMapping("/system/params")
public class SysParamsController {
    
    private static final Logger log = LoggerFactory.getLogger(SysParamsController.class);
    
    @Autowired
    private SysParamsService sysParamsService;
    
    /**
     * 获取系统参数
     */
    @Log(title = "参数设置", operType = "查看")
    @GetMapping
    public R getParams() {
        try {
            SysParams params = sysParamsService.getParams();
            return R.ok().data(params);
        } catch (Exception e) {
            log.error("获取系统参数失败", e);
            return R.error(e.getMessage());
        }
    }
    
    /**
     * 更新系统参数
     */
    @Log(title = "参数设置", operType = "修改")
    @PutMapping
    public R updateParams(@Validated @RequestBody SysParams params) {
        try {
            sysParamsService.updateParams(params);
            return R.ok();
        } catch (Exception e) {
            log.error("更新系统参数失败", e);
            return R.error(e.getMessage());
        }
    }
} 