package com.admin.mapper;

import com.admin.entity.Notice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Map;

@Mapper
public interface NoticeMapper extends BaseMapper<Notice> {
    // 分页查询公告列表
    IPage<Notice> selectNoticePage(Page<Notice> page, @Param("params") Map<String, Object> params);
} 