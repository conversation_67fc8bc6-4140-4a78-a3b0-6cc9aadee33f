<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.ChainConfigMapper">
    <resultMap id="ChainConfigResult" type="com.admin.entity.ChainConfig">
        <id property="id" column="id" />
        <result property="collectAddress" column="collect_address" />
        <result property="collectBalance" column="collect_balance" />
        <result property="bnbAddress" column="bnb_address" />
        <result property="bnbBalance" column="bnb_balance" />
        <result property="bnbPrivateKey" column="bnb_private_key" />
        <result property="withdrawAddress" column="withdraw_address" />
        <result property="withdrawPrivateKey" column="withdraw_private_key" />
        <result property="withdrawBnbBalance" column="withdraw_bnb_balance" />
        <result property="withdrawUsdtBalance" column="withdraw_usdt_balance" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <select id="selectByIdOne" resultMap="ChainConfigResult">
        select * from chain_config where id = 1
    </select>

    <update id="updateByIdOne" parameterType="com.admin.entity.ChainConfig">
        update chain_config
        <set>
            <if test="config.collectAddress != null">collect_address = #{config.collectAddress},</if>
            <if test="config.collectBalance != null">collect_balance = #{config.collectBalance},</if>
            <if test="config.bnbAddress != null">bnb_address = #{config.bnbAddress},</if>
            <if test="config.bnbBalance != null">bnb_balance = #{config.bnbBalance},</if>
            <if test="config.bnbPrivateKey != null">bnb_private_key = #{config.bnbPrivateKey},</if>
            <if test="config.withdrawAddress != null">withdraw_address = #{config.withdrawAddress},</if>
            <if test="config.withdrawPrivateKey != null">withdraw_private_key = #{config.withdrawPrivateKey},</if>
            <if test="config.withdrawBnbBalance != null">withdraw_bnb_balance = #{config.withdrawBnbBalance},</if>
            <if test="config.withdrawUsdtBalance != null">withdraw_usdt_balance = #{config.withdrawUsdtBalance},</if>
        </set>
        where id = 1
    </update>
</mapper> 