package com.admin.service;

import com.admin.entity.RechargeRecord;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.Map;
import java.util.List;

public interface RechargeRecordService extends IService<RechargeRecord> {
    
    IPage<RechargeRecord> getRechargeRecordList(Map<String, Object> params);
    
    Map<String, Object> getRechargeStatistics();
    
    boolean auditRecharge(Long id, Integer auditStatus, String remark);
    
    List<RechargeRecord> getRechargeRecordExportList(Map<String, Object> params);
    
    List<Map<String, Object>> getRechargeTrend(String startDate, String endDate);
} 