package com.admin.service.impl;

import com.admin.entity.RechargeRecord;
import com.admin.mapper.RechargeRecordMapper;
import com.admin.service.RechargeRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import com.admin.entity.FrontUser;
import com.admin.mapper.FrontUserMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.beans.factory.annotation.Autowired;
 
import java.util.List;
 

@Service
public class RechargeRecordServiceImpl extends ServiceImpl<RechargeRecordMapper, RechargeRecord> implements RechargeRecordService {

    @Autowired
    private FrontUserMapper frontUserMapper;

  

    @Override
    public IPage<RechargeRecord> getRechargeRecordList(Map<String, Object> params) {
        int pageNum = params.get("page") == null ? 1 : Integer.parseInt(params.get("page").toString());
        int pageSize = params.get("limit") == null ? 10 : Integer.parseInt(params.get("limit").toString());
        
        Page<RechargeRecord> page = new Page<>(pageNum, pageSize);
        
        LambdaQueryWrapper<RechargeRecord> wrapper = new LambdaQueryWrapper<>();
        
        String username = (String) params.get("username");
        String email = (String) params.get("email");
        String txHash = (String) params.get("txHash");
        Object rechargeType = params.get("rechargeType");
        Object auditStatus = params.get("auditStatus");
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        
        wrapper.like(StringUtils.isNotBlank(username), RechargeRecord::getUsername, username)
                .like(StringUtils.isNotBlank(email), RechargeRecord::getEmail, email)
                .eq(rechargeType != null && !"".equals(rechargeType.toString()), 
                    RechargeRecord::getRechargeType, rechargeType)
                .eq(auditStatus != null && !"".equals(auditStatus.toString()), 
                    RechargeRecord::getAuditStatus, auditStatus)
                .like(StringUtils.isNotBlank(txHash), RechargeRecord::getTxHash, txHash);
        
        if (StringUtils.isNotBlank(startDate)) {
            wrapper.ge(RechargeRecord::getCreateTime, startDate + " 00:00:00");
        }
        if (StringUtils.isNotBlank(endDate)) {
            wrapper.le(RechargeRecord::getCreateTime, endDate + " 23:59:59");
        }
        
        wrapper.orderByDesc(RechargeRecord::getCreateTime);
        
        return this.page(page, wrapper);
    }

    @Override
    public Map<String, Object> getRechargeStatistics() {
        return baseMapper.selectRechargeStatistics();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditRecharge(Long id, Integer auditStatus, String remark) {
        RechargeRecord record = getById(id);
        if (record == null) {
            throw new RuntimeException("充值记录不存在");
        }
        
        if (record.getAuditStatus() != 0) {
            throw new RuntimeException("该记录已审核");
        }
        
        record.setAuditStatus(auditStatus);
        record.setRemark(remark);
        
        boolean updated = updateById(record);
        
        if (updated && auditStatus == 1) {
            LambdaUpdateWrapper<FrontUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(FrontUser::getId, record.getUserId())
                    .setSql("available_balance = available_balance + " + record.getAmount());
            
            int rows = frontUserMapper.update(null, updateWrapper);
            if (rows != 1) {
                throw new RuntimeException("更新用户余额失败");
            }
        }
        
        return true;
    }

    @Override
    public List<RechargeRecord> getRechargeRecordExportList(Map<String, Object> params) {
        // 构建查询条件，与列表查询保持一致
        LambdaQueryWrapper<RechargeRecord> wrapper = new LambdaQueryWrapper<>();
        
        String username = (String) params.get("username");
        String email = (String) params.get("email");
        Object rechargeType = params.get("rechargeType");
        Object auditStatus = params.get("auditStatus");
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        
        // 使用与列表查询相同的条件构建方式
        wrapper.like(StringUtils.isNotBlank(username), RechargeRecord::getUsername, username)
                .like(StringUtils.isNotBlank(email), RechargeRecord::getEmail, email)
                .eq(rechargeType != null && !"".equals(rechargeType.toString()), 
                    RechargeRecord::getRechargeType, rechargeType)
                .eq(auditStatus != null && !"".equals(auditStatus.toString()), 
                    RechargeRecord::getAuditStatus, auditStatus);
        
        if (StringUtils.isNotBlank(startDate)) {
            wrapper.ge(RechargeRecord::getCreateTime, startDate + " 00:00:00");
        }
        if (StringUtils.isNotBlank(endDate)) {
            wrapper.le(RechargeRecord::getCreateTime, endDate + " 23:59:59");
        }
        
        wrapper.orderByDesc(RechargeRecord::getCreateTime);
        
        return this.baseMapper.selectList(wrapper);
    }

    @Override
    public List<Map<String, Object>> getRechargeTrend(String startDate, String endDate) {
        return baseMapper.selectRechargeTrend(startDate, endDate);
    } 
       
} 