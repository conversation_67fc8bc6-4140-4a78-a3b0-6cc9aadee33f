<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.DashboardMapper">
    
    <!-- 获取今日统计数据 -->
    <select id="getDashboardStats" resultType="java.util.Map">
        SELECT 
            -- 今日新增用户数
            (SELECT COUNT(*) 
             FROM front_user
             WHERE DATE(create_time) = CURDATE()) as todayUsers,
            
            -- 今日新增用户数增长率
            (SELECT 
                CASE 
                    WHEN yesterday_count = 0 THEN 0 
                    ELSE ROUND(((today_count - yesterday_count) / yesterday_count) * 100, 1)
                END
             FROM (
                SELECT 
                    (SELECT COUNT(*) FROM front_user WHERE DATE(create_time) = CURDATE()) as today_count,
                    (SELECT COUNT(*) FROM front_user WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) as yesterday_count
             ) t1) as todayUsersRate,
            
            -- 今日充值金额
            (SELECT COALESCE(SUM(amount), 0) 
             FROM recharge_record 
             WHERE DATE(create_time) = CURDATE() AND audit_status = 1) as todayRecharge,
            
            -- 今日充值金额增长率
            (SELECT 
                CASE 
                    WHEN yesterday_amount = 0 THEN 0 
                    ELSE ROUND(((today_amount - yesterday_amount) / yesterday_amount) * 100, 1)
                END
             FROM (
                SELECT 
                    (SELECT COALESCE(SUM(amount), 0) FROM recharge_record WHERE DATE(create_time) = CURDATE() AND audit_status = 1) as today_amount,
                    (SELECT COALESCE(SUM(amount), 0) FROM recharge_record WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND audit_status = 1) as yesterday_amount
             ) t2) as todayRechargeRate,
            
            -- 今日提现金额
            (SELECT COALESCE(SUM(amount), 0) 
             FROM withdraw_record 
             WHERE DATE(create_time) = CURDATE() AND status = 1) as todayWithdraw,
            
            -- 今日提现金额增长率
            (SELECT 
                CASE 
                    WHEN yesterday_amount = 0 THEN 0 
                    ELSE ROUND(((today_amount - yesterday_amount) / yesterday_amount) * 100, 1)
                END
             FROM (
                SELECT 
                    (SELECT COALESCE(SUM(amount), 0) FROM withdraw_record WHERE DATE(create_time) = CURDATE() AND status = 1) as today_amount,
                    (SELECT COALESCE(SUM(amount), 0) FROM withdraw_record WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND status = 1) as yesterday_amount
             ) t3) as todayWithdrawRate
    </select>
    
 
    
</mapper> 