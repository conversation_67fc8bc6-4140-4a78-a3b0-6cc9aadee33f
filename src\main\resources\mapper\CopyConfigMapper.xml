<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.CopyConfigMapper">
    <resultMap id="CopyConfigVOMap" type="com.admin.vo.CopyConfigVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="copyType" column="copy_type"/>
        <result property="minFollowAmount" column="min_follow_amount"/>
        <result property="maxFollowAmount" column="max_follow_amount"/>
        <result property="lockTime" column="lock_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="selectList" resultMap="CopyConfigVOMap">
        SELECT id, name, copy_type, min_follow_amount, max_follow_amount, lock_time, create_time, update_time
        FROM copy_config
        ORDER BY id DESC
    </select>
    <update id="updateById" parameterType="com.admin.entity.CopyConfig">
        UPDATE copy_config
        SET name = #{et.name}, copy_type = #{et.copyType}, min_follow_amount = #{et.minFollowAmount},
            max_follow_amount = #{et.maxFollowAmount}, lock_time = #{et.lockTime}, update_time = NOW()
        WHERE id = #{et.id}
    </update>
</mapper> 