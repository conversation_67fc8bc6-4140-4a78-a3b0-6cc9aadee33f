package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.util.R;
import com.admin.entity.UserBankCard;
import com.admin.service.UserBankCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/user")
public class UserBankCardController {
    
    @Autowired
    private UserBankCardService userBankCardService;
    @Log(title = "银行卡管理", operType = "查询")
    @GetMapping("/{userId}/bank-cards")
    public R<List<UserBankCard>> getBankCards(@PathVariable Long userId) {
        return R.ok(userBankCardService.getUserBankCards(userId));
    }
} 