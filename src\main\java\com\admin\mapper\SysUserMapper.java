package com.admin.mapper;

import com.admin.entity.SysUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    // 分页查询
    IPage<SysUser> selectUserPage(Page<SysUser> page, @Param("username") String username, @Param("status") Integer status);
    
    // 查询所有用户邮箱
    List<Map<String, Object>> selectAllUserEmails();
    
    // 根据邮箱查询用户
    SysUser selectByEmail(@Param("email") String email);
    
    // 统计邮箱数量
    int countByEmail(@Param("email") String email);
    
    // 根据ID查询用户及其角色
    SysUser selectUserWithRole(@Param("id") Long id);
} 