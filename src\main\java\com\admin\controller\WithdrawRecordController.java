package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.WithdrawRecord;
import com.admin.service.WithdrawRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

@Slf4j
@RestController
@RequestMapping("/finance/withdraw")
public class WithdrawRecordController {

    @Autowired
    private WithdrawRecordService withdrawRecordService;
    @Log(title = "提现明细", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        try {
            IPage<WithdrawRecord> page = withdrawRecordService.getWithdrawRecordList(params);
            return R.ok()
                    .put("records", page.getRecords())
                    .put("total", page.getTotal());
        } catch (Exception e) {
            log.error("查询提现记录失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    public R statistics() {
        try {
            Map<String, Object> statistics = withdrawRecordService.getWithdrawStatistics();
            return R.ok().put("data", statistics);
        } catch (Exception e) {
            log.error("查询提现统计失败", e);
            return R.error("查询统计失败：" + e.getMessage());
        }
    }
    @Log(title = "提现明细", operType = "审核")
 
    @PostMapping("/audit/{id}")
    public R audit(@PathVariable Long id, @RequestParam Integer status, @RequestParam String remark) {
        try {
            boolean result = withdrawRecordService.auditWithdraw(id, status, remark);
            return result ? R.ok().put("msg", "审核成功") : R.error("审核失败!");//,请查看服务费余额或者账户余额是否足够,否则就是接收人的信息有误！
        } catch (Exception e) {
            log.error("审核提现申请失败", e);
            return R.error(e.getMessage());
        }
    }
    @Log(title = "提现明细", operType = "导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                      @RequestParam(required = false) String username,
                      @RequestParam(required = false) String status,
                      @RequestParam(required = false) String startDate,
                      @RequestParam(required = false) String endDate) {
        try {
            List<WithdrawRecord> list = withdrawRecordService.exportWithdrawRecord(username, status, startDate, endDate);
            
            // 设置序号
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setIndex(i + 1);
            }
            
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("提现记录", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 创建excel导出对象
            EasyExcel.write(response.getOutputStream(), WithdrawRecord.class)
                .sheet("提现记录")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自适应列宽
                .doWrite(list);
            
        } catch (Exception e) {
            log.error("导出提现记录失败", e);
        }
    }
}