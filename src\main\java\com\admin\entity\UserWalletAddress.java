package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("user_wallet_address")
public class UserWalletAddress {
    
    /** ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /** 链名称 */
    private String chainName;
    
    /** 链地址 */
    private String chainAddress;
    
    /** 私钥 */
    private String privateKey;
    
    /** 账户bnb余额 */
    private BigDecimal bnbBalance;
    
    /** 账户usdt余额 */
    private BigDecimal usdtBalance;
    
    /** 用户ID */
    private Long userId;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;
} 