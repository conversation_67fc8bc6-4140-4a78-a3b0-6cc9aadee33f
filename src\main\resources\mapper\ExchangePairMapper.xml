<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.ExchangePairMapper">
    
    <resultMap type="com.admin.entity.ExchangePairInfo" id="ExchangePairResult">
        <id     property="id"           column="id"            />
        <result property="exchangeName" column="exchange_name" />
        <result property="tokenName"    column="token_name"    />
        <result property="pairName"     column="pair_name"     />
        <result property="logoUrl"      column="logo_url"      />
        <result property="apiUrl"       column="api_url"       />
        <result property="specialProcessing" column="special_processing" />
        <result property="sort"         column="sort"          />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <sql id="selectExchangePairVo">
        select id, exchange_name, token_name, pair_name, logo_url, api_url, special_processing, sort, is_enabled, create_time, update_time
        from exchange_pair_info
    </sql>

    <select id="selectExchangePairList" parameterType="com.admin.entity.ExchangePairInfo" resultMap="ExchangePairResult">
        <include refid="selectExchangePairVo"/>
        <where>
            <if test="exchangeName != null and exchangeName != ''">
                AND exchange_name like concat('%', #{exchangeName}, '%')
            </if>
            <if test="tokenName != null and tokenName != ''">
                AND token_name like concat('%', #{tokenName}, '%')
            </if>
            <if test="pairName != null and pairName != ''">
                AND pair_name like concat('%', #{pairName}, '%')
            </if>
            <if test="specialProcessing != null">
                AND special_processing = #{specialProcessing}
            </if>
            <if test="isEnabled != null">
                AND is_enabled = #{isEnabled}
            </if>
        </where>
        order by sort asc, create_time desc
    </select>
    
    <select id="selectExchangePairById" parameterType="Long" resultMap="ExchangePairResult">
        <include refid="selectExchangePairVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertExchangePair" parameterType="com.admin.entity.ExchangePairInfo" useGeneratedKeys="true" keyProperty="id">
        insert into exchange_pair_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="exchangeName != null">exchange_name,</if>
            <if test="tokenName != null">token_name,</if>
            <if test="pairName != null">pair_name,</if>
            <if test="logoUrl != null">logo_url,</if>
            <if test="apiUrl != null and apiUrl != ''">api_url,</if>
            <if test="specialProcessing != null">special_processing,</if>
            <if test="sort != null">sort,</if>
            <if test="isEnabled != null">is_enabled,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="exchangeName != null">#{exchangeName},</if>
            <if test="tokenName != null">#{tokenName},</if>
            <if test="pairName != null">#{pairName},</if>
            <if test="logoUrl != null">#{logoUrl},</if>
            <if test="apiUrl != null and apiUrl != ''">#{apiUrl},</if>
            <if test="specialProcessing != null">#{specialProcessing},</if>
            <if test="sort != null">#{sort},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            sysdate(),
            sysdate()
        </trim>
    </insert>

    <update id="updateExchangePair" parameterType="com.admin.entity.ExchangePairInfo">
        update exchange_pair_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="exchangeName != null">exchange_name = #{exchangeName},</if>
            <if test="tokenName != null">token_name = #{tokenName},</if>
            <if test="pairName != null">pair_name = #{pairName},</if>
            <if test="logoUrl != null">logo_url = #{logoUrl},</if>
            <if test="apiUrl != null">api_url = #{apiUrl},</if>
            <if test="specialProcessing != null">special_processing = #{specialProcessing},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExchangePairByIds" parameterType="Long">
        delete from exchange_pair_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 分页查询交易对列表 -->
    <select id="getExchangePairPage" resultMap="ExchangePairResult">
        <include refid="selectExchangePairVo"/>
        <where>
            <if test="exchangeName != null and exchangeName != ''">
                AND exchange_name like concat('%', #{exchangeName}, '%')
            </if>
            <if test="pairName != null and pairName != ''">
                AND pair_name like concat('%', #{pairName}, '%')
            </if>
            <if test="specialProcessing != null">
                AND special_processing = #{specialProcessing}
            </if>
            <if test="isEnabled != null">
                AND is_enabled = #{isEnabled}
            </if>
        </where>
        order by sort asc, create_time desc
    </select>

    <select id="getEnabledPairNames" resultType="String">
        select pair_name from exchange_pair_info where is_enabled = 1 order by sort asc
    </select>

    <select id="selectEnabledPairNames" resultType="java.lang.String">
        SELECT pair_name FROM exchange_pair_info WHERE is_enabled = 1
    </select>
</mapper> 