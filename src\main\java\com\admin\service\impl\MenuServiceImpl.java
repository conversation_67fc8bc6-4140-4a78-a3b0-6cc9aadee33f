package com.admin.service.impl;

import com.admin.entity.SysMenu;
import com.admin.entity.SysRoleMenu;
import com.admin.mapper.SysMenuMapper;
import com.admin.mapper.SysRoleMenuMapper;
import com.admin.service.MenuService;
import com.admin.vo.MenuVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MenuServiceImpl implements MenuService {

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Override
    public List<MenuVO> getUserMenus(Long userId) {
        List<SysMenu> menus = menuMapper.selectMenusByUserId(userId);
        log.info("Found {} raw menus from database for user {}", menus.size(), userId);
        
        List<MenuVO> menuTree = buildMenuTree(menus, 0L);
        log.info("Built menu tree with {} top-level items", menuTree.size());
        
        return menuTree;
    }

    @Override
    public List<MenuVO> getMenuTree() {
        // 获取所有菜单列表
        List<SysMenu> menuList = menuMapper.selectList(new LambdaQueryWrapper<SysMenu>()
                .orderByAsc(SysMenu::getOrderNum));
        
        // 构建树形结构
        return buildMenuTree(menuList, 0L);
    }

    @Override
    public List<Long> getRoleMenuIds(Long roleId) {
        return roleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>()
                .eq(SysRoleMenu::getRoleId, roleId))
                .stream()
                .map(SysRoleMenu::getMenuId)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void assignRoleMenu(Long roleId, List<Long> menuIds) {
        // 先删除原有权限
        roleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>()
                .eq(SysRoleMenu::getRoleId, roleId));
        
        // 批量插入新权限
        if (menuIds != null && !menuIds.isEmpty()) {
            List<SysRoleMenu> roleMenuList = menuIds.stream().map(menuId -> {
                SysRoleMenu roleMenu = new SysRoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(menuId);
                return roleMenu;
            }).collect(Collectors.toList());
            
            roleMenuMapper.insertBatch(roleMenuList);
        }
    }

    private List<MenuVO> buildMenuTree(List<SysMenu> menuList, Long parentId) {
        List<MenuVO> tree = new ArrayList<>();
        
        for (SysMenu menu : menuList) {
            if (menu.getParentId().equals(parentId)) {
                MenuVO node = convertToVO(menu);
                node.setChildren(buildMenuTree(menuList, menu.getId()));
                tree.add(node);
            }
        }
        
        return tree;
    }

    private MenuVO convertToVO(SysMenu menu) {
        MenuVO vo = new MenuVO();
        BeanUtils.copyProperties(menu, vo);
        vo.setId(menu.getId());
        vo.setLabel(menu.getMenuName());
        vo.setName(menu.getMenuName());
        
        // 处理路径，移除开头的斜杠
        if (menu.getPath() != null) {
            vo.setPath(menu.getPath().startsWith("/") ? 
                menu.getPath().substring(1) : menu.getPath());
        }
        
        return vo;
    }
} 