package com.admin.service;

import com.admin.dto.AdminUpdateDTO;
import com.admin.entity.SysUser;
import com.admin.exception.ServiceException;
import com.admin.mapper.SysUserMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.time.LocalDateTime;

@Service
public class AdminService extends ServiceImpl<SysUserMapper, SysUser> {

    /**
     * 更新管理员信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAdminInfo(Long adminId, AdminUpdateDTO dto) {
        // 检查管理员是否存在
        SysUser user = getById(adminId);
        if (user == null) {
            throw new ServiceException("管理员不存在");
        }
        
        // 检查手机号是否被其他管理员使用
        if (StringUtils.hasText(dto.getPhone())) {
            Long count = lambdaQuery()
                .eq(SysUser::getPhone, dto.getPhone())
                .ne(SysUser::getId, adminId)
                .count();
            if (count > 0) {
                throw new ServiceException("手机号已被使用");
            }
        }
        
        // 检查邮箱是否被其他管理员使用
        if (StringUtils.hasText(dto.getEmail())) {
            Long count = lambdaQuery()
                .eq(SysUser::getEmail, dto.getEmail())
                .ne(SysUser::getId, adminId)
                .count();
            if (count > 0) {
                throw new ServiceException("邮箱已被使用");
            }
        }
        
        // 更新管理员信息 - 只更新修改的字段
        user.setNickname(dto.getNickname());
        user.setPhone(dto.getPhone());
        user.setEmail(dto.getEmail());
        user.setUpdateTime(LocalDateTime.now());
        
        // 执行更新
        if (!updateById(user)) {
            throw new ServiceException("更新管理员信息失败");
        }
    }
} 