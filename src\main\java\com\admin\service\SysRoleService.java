package com.admin.service;

import com.admin.entity.SysRole;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

public interface SysRoleService extends IService<SysRole> {
    
    /**
     * 获取角色列表
     */
    List<SysRole> getRoleList(String name);
    
    /**
     * 新增角色
     */
    void addRole(SysRole role);
    
    /**
     * 修改角色
     */
    void updateRole(SysRole role);
    
    /**
     * 删除角色
     */
    void deleteRole(Long id);
    
    /**
     * 更新角色状态
     */
    void updateRoleStatus(Long id, Integer status);
    
    /**
     * 分配权限
     */
    void assignPermissions(Long roleId, List<Long> menuIds);
} 