# WebSocket到REST API数据转换示例

## 输入数据 (WebSocket格式)
```json
{
  "stream": "btcusdt@ticker",
  "data": {
    "e": "24hrTicker",
    "s": "BTCUSDT",
    "p": "1465.12000000",
    "P": "1.243",
    "w": "118802.75972344",
    "c": "119312.94000000",
    "Q": "0.02226000",
    "o": "117847.82000000",
    "h": "119766.65000000",
    "l": "117840.00000000",
    "v": "9589.74902000",
    "q": "1139288648.63115010",
    "O": 1753576956012,
    "C": 1753663356012,
    "F": "5114264421",
    "L": "5115401124",
    "n": "1136704"
  }
}
```

## 转换映射规则
| WebSocket字段 | REST API字段 | 转换规则 |
|---------------|--------------|----------|
| s | symbol | 直接映射 |
| p | priceChange | 直接映射 |
| P | priceChangePercent | 直接映射 |
| w | weightedAvgPrice | 直接映射 |
| o | prevClosePrice | 使用openPrice作为prevClosePrice |
| c | lastPrice | 直接映射 |
| Q | lastQty | 直接映射 |
| c | bidPrice | 使用lastPrice作为bidPrice |
| - | bidQty | 固定值 "0.00000000" |
| c | askPrice | 使用lastPrice作为askPrice |
| - | askQty | 固定值 "0.00000000" |
| o | openPrice | 直接映射 |
| h | highPrice | 直接映射 |
| l | lowPrice | 直接映射 |
| v | volume | 直接映射 |
| q | quoteVolume | 直接映射 |
| O | openTime | 直接映射 |
| C | closeTime | 直接映射 |
| F | firstId | 直接映射 |
| L | lastId | 直接映射 |
| n | count | 直接映射 |

## 输出数据 (REST API格式)
```json
{
  "symbol": "BTCUSDT",
  "priceChange": "1465.12000000",
  "priceChangePercent": "1.243",
  "weightedAvgPrice": "118802.75972344",
  "prevClosePrice": "117847.82000000",
  "lastPrice": "119312.94000000",
  "lastQty": "0.02226000",
  "bidPrice": "119312.94000000",
  "bidQty": "0.00000000",
  "askPrice": "119312.94000000",
  "askQty": "0.00000000",
  "openPrice": "117847.82000000",
  "highPrice": "119766.65000000",
  "lowPrice": "117840.00000000",
  "volume": "9589.74902000",
  "quoteVolume": "1139288648.63115010",
  "openTime": 1753576956012,
  "closeTime": 1753663356012,
  "firstId": 5114264421,
  "lastId": 5115401124,
  "count": 1136704
}
```

## 对比目标格式 (REST API原始)
```json
{
  "symbol": "BTCUSDT",
  "priceChange": "1683.11000000",
  "priceChangePercent": "1.427",
  "weightedAvgPrice": "118785.28447027",
  "prevClosePrice": "117924.57000000",
  "lastPrice": "119607.67000000",
  "lastQty": "0.01623000",
  "bidPrice": "119607.67000000",
  "bidQty": "7.49954000",
  "askPrice": "119607.68000000",
  "askQty": "1.86431000",
  "openPrice": "117924.56000000",
  "highPrice": "119766.65000000",
  "lowPrice": "117825.50000000",
  "volume": "9463.93410000",
  "quoteVolume": "1124176104.27637580",
  "openTime": 1753575854013,
  "closeTime": 1753662254013,
  "firstId": 5114256467,
  "lastId": 5115373870,
  "count": 1117404
}
```

## ✅ 验证结果
- **字段名**: 完全匹配 ✅
- **数据类型**: 完全匹配 ✅
- **字段数量**: 完全匹配 ✅
- **JSON结构**: 完全匹配 ✅

现在WebSocket输出的数据格式与REST API完全一致！
