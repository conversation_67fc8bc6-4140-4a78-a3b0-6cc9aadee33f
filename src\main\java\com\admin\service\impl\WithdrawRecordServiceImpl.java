package com.admin.service.impl;
 
import com.admin.entity.WithdrawRecord;
import com.admin.mapper.WithdrawRecordMapper;
import com.admin.service.WithdrawRecordService;
import com.admin.service.ChainConfigService;
import com.admin.entity.ChainConfig;
import com.admin.utils.Web3TransferUtil;
import org.springframework.beans.factory.annotation.Autowired;
 
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
 

 
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import org.springframework.transaction.interceptor.TransactionAspectSupport;

 

@Slf4j
@Service
public class WithdrawRecordServiceImpl extends ServiceImpl<WithdrawRecordMapper, WithdrawRecord> implements WithdrawRecordService {
    @Autowired
    private ChainConfigService chainConfigService;
    

    @Override
    public IPage<WithdrawRecord> getWithdrawRecordList(Map<String, Object> params) {
        log.info("查询提现记录参数: {}", params);

        int pageNum = params.get("page") == null ? 1 : Integer.parseInt(params.get("page").toString());
        int pageSize = params.get("limit") == null ? 10 : Integer.parseInt(params.get("limit").toString());

        Page<WithdrawRecord> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectWithdrawRecordList(page, params);
    }

    @Override
    public Map<String, Object> getWithdrawStatistics() {
        return baseMapper.selectWithdrawStatistics();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditWithdraw(Long id, Integer status, String remark) {

        WithdrawRecord record = this.getById(id);
        if (record == null) {
            throw new RuntimeException("提现记录不存在");
        }

        if (record.getStatus() != 0) {
            throw new RuntimeException("该记录已审核");
        }

        // 开始处理
        try {
            if (status == 1) { // 审核通过，先链上转账
                ChainConfig chainConfig = chainConfigService.getConfig();
                String fromAddress = chainConfig.getWithdrawAddress();
                String encryptedPrivateKey = chainConfig.getWithdrawPrivateKey();
                if (fromAddress == null || fromAddress.isEmpty() || encryptedPrivateKey == null || encryptedPrivateKey.isEmpty()) {
                    throw new RuntimeException("链端提现地址或私钥未配置");
                }
                // 校验余额（链端余额已在工具类校验）
                String toAddress = record.getAddress();
                if (toAddress == null || toAddress.isEmpty()) {
                    throw new RuntimeException("提现接收地址为空");
                }
                // 发起USDT链上转账
                String txHash = Web3TransferUtil.transferUsdt(fromAddress, encryptedPrivateKey, toAddress, record.getRealAmount());
                // 写入txHash
                record.setTxHash(txHash);
                record.setStatus(status);
                record.setRemark("链上转账成功，txHash:" + txHash);
                record.setUpdateTime(LocalDateTime.now());
                boolean updateResult = this.updateById(record);
                if (!updateResult) {
                    throw new RuntimeException("更新提现记录失败");
                }
                // 从冻结余额中扣除
                boolean deductResult = baseMapper.updateUserFrozenBalance(record.getUserId(), record.getAmount());
                if (!deductResult) {
                    throw new RuntimeException("扣除冻结余额失败");
                }
                return true;
            } else if (status == 2 || status == 3) { // 拒绝或未通过
                // 1. 更新提现记录状态
                record.setStatus(status);
                record.setRemark(remark);
                record.setUpdateTime(LocalDateTime.now());
                boolean updateResult = this.updateById(record);
                if (!updateResult) {
                    throw new RuntimeException("更新提现记录失败");
                }
                // 从冻结余额扣除并返还到可用余额
                boolean returnResult = baseMapper.updateUserBalance(record.getUserId(), record.getAmount());
                if (!returnResult) {
                    throw new RuntimeException("返还余额失败");
                }
            }

            return true;
        } catch (Exception e) {
            log.error("审核提现申请失败", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RuntimeException("审核处理失败: " + e.getMessage());
        }
    } 

    @Override
    public List<WithdrawRecord> exportWithdrawRecord(String username, String status, String startDate, String endDate) {
        return baseMapper.selectWithdrawRecordForExport(username, status, startDate, endDate);
    }
} 