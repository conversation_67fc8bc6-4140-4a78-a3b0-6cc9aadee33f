package com.admin.service;

import com.admin.entity.ExchangePairInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 交易对管理Service接口
 */
public interface ExchangePairService {
    /**
     * 查询交易对列表（分页）
     */
    IPage<ExchangePairInfo> getExchangePairPage(Integer pageNum, Integer pageSize, String exchangeName, String pairName, Integer specialProcessing, Integer isEnabled);

    /**
     * 查询交易对列表
     * 
     * @param exchangePair 交易对信息
     * @return 交易对集合
     */
    List<ExchangePairInfo> selectExchangePairList(ExchangePairInfo exchangePair);

    /**
     * 查询交易对信息
     * 
     * @param id 交易对ID
     * @return 交易对信息
     */
    ExchangePairInfo selectExchangePairById(Long id);

    /**
     * 新增交易对
     * 
     * @param exchangePair 交易对信息
     * @return 结果
     */
    int insertExchangePair(ExchangePairInfo exchangePair);

    /**
     * 修改交易对
     * 
     * @param exchangePair 交易对信息
     * @return 结果
     */
    int updateExchangePair(ExchangePairInfo exchangePair);

    /**
     * 批量删除交易对
     * 
     * @param ids 需要删除的交易对ID数组
     * @return 结果
     */
    int deleteExchangePairByIds(Long[] ids);

    /**
     * 修改交易对状态
     * 
     * @param exchangePair 交易对信息
     * @return 结果
     */
    int updateExchangePairStatus(ExchangePairInfo exchangePair);

    /**
     * 获取所有is_enabled=1的pair_name
     * 
     * @return 交易对名称集合
     */
    List<String> getEnabledPairNames();
} 