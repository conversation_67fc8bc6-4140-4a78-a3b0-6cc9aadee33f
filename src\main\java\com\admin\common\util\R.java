package com.admin.common.util;

import lombok.Data;
import java.io.Serializable;
import java.util.HashMap;

@Data
public class R<T> extends HashMap<String, Object> implements Serializable {
    private static final long serialVersionUID = 1L;

    public R() {
        put("code", 0);
        put("msg", "success");
    }

    public static <T> R<T> ok() {
        return new R<T>();
    }

    public static <T> R<T> ok(T data) {
        R<T> r = new R<T>();
        r.put("data", data);
        return r;
    }

    public static <T> R<T> fail() {
        return fail("操作失败");
    }

    public static <T> R<T> fail(String msg) {
        R<T> r = new R<T>();
        r.put("code", -1);
        r.put("msg", msg);
        return r;
    }

    public static <T> R<T> fail(int code, String msg) {
        R<T> r = new R<T>();
        r.put("code", code);
        r.put("msg", msg);
        return r;
    }

    @Override
    public R<T> put(String key, Object value) {
        super.put(key, value);
        return this;
    }
} 