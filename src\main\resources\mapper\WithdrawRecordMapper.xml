<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.WithdrawRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.admin.entity.WithdrawRecord">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="register_email" property="registerEmail" />
        <result column="amount" property="amount" />
        <result column="fee" property="fee" />
        <result column="real_amount" property="realAmount" />
        <result column="address" property="address" />
        <result column="chain_name" property="chainName" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 添加这个SQL片段定义 -->
    <sql id="Base_Column_List">
        id, user_id, username, register_email, amount, fee, real_amount, 
        address, chain_name, status, remark, create_time, update_time
    </sql>

    <!-- 分页查询提现记录 -->
    <select id="selectWithdrawRecordList" resultMap="BaseResultMap">
        SELECT 
            w.*,
            f.email as register_email
        FROM withdraw_record w
        LEFT JOIN front_user f ON w.user_id = f.id
        <where>
            w.user_id>1
            <if test="params.username != null and params.username != ''">
                AND (w.username LIKE CONCAT('%', #{params.username}, '%')
                OR w.register_email LIKE CONCAT('%', #{params.username}, '%')
                OR f.email LIKE CONCAT('%', #{params.username}, '%'))
            </if>
            <if test="params.status != null and params.status != ''">
                AND w.status = #{params.status}
            </if>
            <if test="params.startDate != null and params.startDate != ''">
                AND w.create_time >= #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                AND w.create_time &lt;= #{params.endDate}
            </if>
        </where>
        ORDER BY w.create_time DESC
    </select>

    <!-- 查询提现统计信息 -->
    <select id="selectWithdrawStatistics" resultType="java.util.Map">
        SELECT 
            SUM(CASE WHEN DATE(create_time) = CURDATE() THEN amount ELSE 0 END) as todayAmount,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayCount,
            SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as pendingAmount,
            COUNT(CASE WHEN status = 0 THEN 1 END) as pendingCount,
            SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as totalPassAmount
        FROM withdraw_record where user_id>1
    </select>

    <!-- 更新用户账户余额 -->
    <update id="updateUserBalance">
        UPDATE front_user 
        SET available_balance = available_balance + #{amount},
            frozen_balance = frozen_balance - #{amount},
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 更新用户冻结余额 -->
    <update id="updateUserFrozenBalance">
        UPDATE front_user 
        SET frozen_balance = frozen_balance - #{amount},
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 查询所有提现记录(导出用) -->
    <select id="selectWithdrawRecordForExport" resultMap="BaseResultMap">
        SELECT 
            w.*,
            f.email as register_email
        FROM withdraw_record w
        LEFT JOIN front_user f ON w.user_id = f.id
        <where>
            w.user_id>1
            <if test="username != null and username != ''">
                AND (w.username LIKE CONCAT('%', #{username}, '%')
                OR w.register_email LIKE CONCAT('%', #{username}, '%')
                OR f.email LIKE CONCAT('%', #{username}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND w.status = #{status}
            </if>
            <if test="startDate != null and startDate != ''">
                AND w.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND w.create_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY w.create_time DESC
    </select>

    <!-- 查询状态为0且金额小于指定金额的提现记录 -->
    <select id="selectPendingWithdrawRecordsByAmount" resultMap="BaseResultMap">
        SELECT 
            w.*,
            f.email as register_email
        FROM withdraw_record w
        LEFT JOIN front_user f ON w.user_id = f.id
        WHERE w.status = 0 
            AND w.amount &lt;= #{maxAmount}
            AND w.user_id > 1
        ORDER BY w.create_time ASC
    </select>

</mapper> 