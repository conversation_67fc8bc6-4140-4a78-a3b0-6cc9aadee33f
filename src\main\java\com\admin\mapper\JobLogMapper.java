package com.admin.mapper;

import com.admin.entity.JobLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface JobLogMapper extends BaseMapper<JobLog> {
    /**
     * 查询任务日志列表
     */
    IPage<JobLog> selectJobLogList(Page<JobLog> page, @Param("jobId") Long jobId);
} 