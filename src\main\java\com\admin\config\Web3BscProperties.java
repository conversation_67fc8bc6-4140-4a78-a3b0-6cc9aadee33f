package com.admin.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "web3.bsc")
public class Web3BscProperties {
    private List<String> rpcUrls;
    private String usdtContract;

    public List<String> getRpcUrls() {
        return rpcUrls;
    }
    public void setRpcUrls(List<String> rpcUrls) {
        this.rpcUrls = rpcUrls;
    }
    public String getUsdtContract() {
        return usdtContract;
    }
    public void setUsdtContract(String usdtContract) {
        this.usdtContract = usdtContract;
    }
} 