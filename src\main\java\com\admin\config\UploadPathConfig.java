package com.admin.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UploadPathConfig {
    @Value("${upload.env:local}")
    private String env;
    @Value("${upload.path-local}")
    private String pathLocal;
    @Value("${upload.path-prod}")
    private String pathProd;

    public String getUploadPath() {
        return "prod".equalsIgnoreCase(env) ? pathProd : pathLocal;
    }

    /**
     * 获取Banner图片上传路径
     */
    public String getBannerUploadPath() {
        return getUploadPath() + "/banner";
    }

    /**
     * 获取Logo图片上传路径
     */
    public String getLogoUploadPath() {
        return getUploadPath() + "/logo";
    }
} 