package com.admin.service;

import com.admin.entity.TransferRecord;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import com.admin.entity.excel.TransferRecordExcel;

public interface TransferRecordService extends IService<TransferRecord> {
    
    /**
     * 分页查询转账记录
     */
    IPage<TransferRecord> getTransferRecordList(Map<String, Object> params);
    
    /**
     * 查询转账统计信息
     */
    Map<String, Object> getTransferStatistics();
    
    /**
     * 导出转账记录
     */
    List<TransferRecordExcel> exportTransferRecord(String fromUsername, String toUsername, Integer status,
                                                   String startDate, String endDate);
} 