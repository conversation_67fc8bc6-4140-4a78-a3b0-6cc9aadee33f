package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.common.utils.SecurityUtils;
import com.admin.dto.AdminUpdateDTO;
import com.admin.dto.PasswordUpdateDTO;
import com.admin.entity.SysUser;
import com.admin.mapper.SysUserMapper;
import com.admin.service.AdminService;
import com.admin.service.AuthService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;

@RestController
@RequestMapping("/admin")
@CrossOrigin
@Slf4j
@Validated
public class AdminController {
    
    @Autowired
    private AdminService adminService;
    
    @Autowired
    private AuthService authService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private SysUserMapper sysUserMapper;
    @Log(title = "管理员列表", operType = "查询")
    @GetMapping("/info")
    public R getUserInfo() {
        SysUser user = authService.getUserInfo();
        return R.ok().data(user);
    }
    @Log(title = "管理员列表", operType = "修改")
    @PostMapping("/info/update")
    public R updateAdminInfo(@Valid @RequestBody AdminUpdateDTO adminUpdateDTO) {
        Long adminId = SecurityUtils.getUserId();
        
        // 检查手机号是否已存在
        if (adminUpdateDTO.getPhone() != null) {
            SysUser existingUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getPhone, adminUpdateDTO.getPhone())
                    .ne(SysUser::getId, adminId)  // 排除当前用户
            );
            
            if (existingUser != null) {
                return R.error("手机号码已被使用");
            }
        }
        
        adminService.updateAdminInfo(adminId, adminUpdateDTO);
        return R.ok();
    }
    @Log(title = "管理员列表", operType = "修改密码")
    @PostMapping("/password")
    public R updatePassword(@RequestBody PasswordUpdateDTO passwordDTO) {
        Long userId = SecurityUtils.getUserId();
        SysUser user = sysUserMapper.selectById(userId);
        
        if (!passwordEncoder.matches(passwordDTO.getOldPassword(), user.getPassword())) {
            return R.error("原密码不正确");
        }
        
        user.setPassword(passwordEncoder.encode(passwordDTO.getNewPassword()));
        authService.updatePassword(user);
        
        return R.ok();
    }
} 