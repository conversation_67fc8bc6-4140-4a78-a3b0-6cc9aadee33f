package com.admin.service.impl;

import com.admin.entity.SysJobLog;
import com.admin.mapper.SysJobLogMapper;
import com.admin.service.SysJobLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;

@Service
public class SysJobLogServiceImpl extends ServiceImpl<SysJobLogMapper, SysJobLog> implements SysJobLogService {

    @Override
    public IPage<SysJobLog> getJobLogList(String jobName,String jobType, String executionResult,
                                         LocalDateTime startDate, LocalDateTime endDate,
                                         Integer page, Integer limit) {
        LambdaQueryWrapper<SysJobLog> wrapper = new LambdaQueryWrapper<>();
        
        wrapper.eq(StringUtils.isNotBlank(jobType), SysJobLog::getJobType, jobType)
        .eq(StringUtils.isNotBlank(jobName), SysJobLog::getJobName, jobName)  // 添加jobName条件
               .eq(StringUtils.isNotBlank(executionResult), SysJobLog::getExecutionResult, executionResult)
               .ge(startDate != null, SysJobLog::getExecutionTime, startDate)
               .le(endDate != null, SysJobLog::getExecutionTime, endDate)
               .orderByDesc(SysJobLog::getExecutionTime);

        return page(new Page<>(page, limit), wrapper);
    }

    @Override
    public boolean cleanJobLog() {
        return remove(new LambdaQueryWrapper<>());
    }
} 