package com.admin.common.utils;

import com.admin.common.exception.BusinessException;
import com.admin.service.SysUserService;
import com.admin.entity.SysUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SecurityUtils {
    
    private static SysUserService sysUserService;
    
    @Autowired
    public void setSysUserService(SysUserService sysUserService) {
        SecurityUtils.sysUserService = sysUserService;
    }
    
    /**
     * 获取当前登录用户ID
     */
    public static Long getUserId() {
        String token = ServletUtils.getRequest().getHeader("Authorization");
        if (StringUtils.isBlank(token)) {
            throw new BusinessException("未登录");
        }
        token = token.replace("Bearer ", "");
        return JwtUtils.getUserIdFromToken(token);
    }
    
    /**
     * 获取当前登录用户名
     */
    public static String getUsername() {
        Long userId = getUserId();
        if (userId != null && sysUserService != null) {
            SysUser user = sysUserService.getUserById(userId);
            return user != null ? user.getUsername() : "";
        }
        return "";
    }
}