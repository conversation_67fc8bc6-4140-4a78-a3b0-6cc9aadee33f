package com.admin.utils;

import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;
import org.web3j.crypto.Credentials;
import org.web3j.tx.gas.DefaultGasProvider;
import org.web3j.tx.Transfer;
import org.web3j.tx.RawTransactionManager;
import org.web3j.tx.gas.ContractGasProvider;
import org.web3j.tx.gas.StaticGasProvider;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.datatypes.Function;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class Web3TransferUtil {
    // BSC主网USDT合约
    private static final String USDT_CONTRACT = "******************************************";
    // USDT精度
    private static final int USDT_DECIMALS = 18;
    // 默认节点
    private static final String BSC_RPC = "https://bsc-dataseed3.bnbchain.org";

    /**
     * USDT转账
     * @param fromAddress 转出地址
     * @param encryptedPrivateKey 加密私钥
     * @param toAddress 接收地址
     * @param amount 金额（单位：USDT，非最小单位）
     * @return 交易哈希
     * @throws Exception 失败抛出异常
     */
    public static String transferUsdt(String fromAddress, String encryptedPrivateKey, String toAddress, BigDecimal amount) throws Exception {
        if (fromAddress == null || fromAddress.isEmpty() || encryptedPrivateKey == null || encryptedPrivateKey.isEmpty()) {
            throw new RuntimeException("转出地址或私钥为空");
        }
        Web3j web3j = Web3j.build(new HttpService(BSC_RPC));
        // 1. 解密私钥
        String privateKey = AESUtil.decrypt(encryptedPrivateKey);
        Credentials credentials = Credentials.create(privateKey);
        // 2. 查询USDT余额
        BigInteger usdtBalance = getUsdtBalance(web3j, fromAddress);
        BigInteger realAmount = amount.movePointRight(USDT_DECIMALS).toBigInteger();
        if (usdtBalance.compareTo(realAmount) < 0) {
            throw new RuntimeException("提现地址USDT余额不足");
        }
        // 3. 构造ERC20 transfer方法
        Function function = new Function(
                "transfer",
                Arrays.asList(new Address(toAddress), new Uint256(realAmount)),
                Collections.emptyList()
        );
        String encodedFunction = FunctionEncoder.encode(function);
        // 4. 获取nonce
        EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                fromAddress, DefaultBlockParameterName.PENDING).send();
        BigInteger nonce = ethGetTransactionCount.getTransactionCount();
        // 5. 发送交易
        RawTransactionManager txManager = new RawTransactionManager(web3j, credentials);
        ContractGasProvider gasProvider = new StaticGasProvider(BigInteger.valueOf(5_000_000_000L), BigInteger.valueOf(150_000));
        org.web3j.protocol.core.methods.response.EthSendTransaction response = txManager.sendTransaction(
                gasProvider.getGasPrice(USDT_CONTRACT),
                gasProvider.getGasLimit(USDT_CONTRACT),
                USDT_CONTRACT,
                encodedFunction,
                BigInteger.ZERO
        );
        if (response.hasError()) {
            throw new RuntimeException("USDT转账失败: " + response.getError().getMessage());
        }
        return response.getTransactionHash();
    }

    /**
     * 查询USDT余额
     */
    public static BigInteger getUsdtBalance(Web3j web3j, String address) throws Exception {
        try {
            // 防御性处理钱包地址
            String cleanAddress = address == null ? "" : address.trim();
            if (cleanAddress.startsWith("0x") || cleanAddress.startsWith("0X")) {
                cleanAddress = cleanAddress.substring(2);
            }
            if (cleanAddress.length() > 40) {
                cleanAddress = cleanAddress.substring(0, 40);
            }
            

            // 防御性处理合约地址
            String contractAddr = USDT_CONTRACT == null ? "" : USDT_CONTRACT.trim();
            if (contractAddr.startsWith("0x") || contractAddr.startsWith("0X")) {
                contractAddr = contractAddr.substring(2);
            }
            contractAddr = "0x" + contractAddr;
            if (contractAddr.length() > 42) {
                contractAddr = contractAddr.substring(0, 42);
            }
            

            Function function = new Function(
                    "balanceOf",
                    Collections.singletonList(new Address(cleanAddress)),
                    Collections.singletonList(new org.web3j.abi.TypeReference<Uint256>() {})
            );
            String data = FunctionEncoder.encode(function);
            String fromAddr = "0x" + cleanAddress;
            org.web3j.protocol.core.methods.response.EthCall response = web3j.ethCall(
                    org.web3j.protocol.core.methods.request.Transaction.createEthCallTransaction(fromAddr, contractAddr, data),
                    DefaultBlockParameterName.LATEST).send();

            if (response.getError() != null) {
                throw new RuntimeException("USDT余额查询节点返回错误: " + response.getError().getMessage());
            }
            

            List<org.web3j.abi.datatypes.Type> results = org.web3j.abi.FunctionReturnDecoder.decode(
                    response.getValue(), function.getOutputParameters());
            if (!results.isEmpty()) {
                return new BigInteger(results.get(0).getValue().toString());
            } else {
                throw new RuntimeException("USDT余额查询返回结果为空");
            }
        } catch (Exception e) {
            throw new RuntimeException("查询USDT余额失败: " + e.getMessage(), e);
        }
    }
} 