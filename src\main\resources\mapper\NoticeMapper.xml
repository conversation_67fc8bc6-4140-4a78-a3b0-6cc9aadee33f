<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.NoticeMapper">
    
    <!-- 分页查询公告列表 -->
    <select id="selectNoticePage" resultType="com.admin.entity.Notice">
        SELECT * FROM notice
        <where>
            <if test="params.title != null and params.title != ''">
                AND title LIKE CONCAT('%', #{params.title}, '%')
            </if>
            <if test="params.type != null and params.type != ''">
                AND notice_type = #{params.type}
            </if>
            <if test="params.status != null and params.status != ''">
                AND status = #{params.status}
            </if>
            <if test="params.startDate != null and params.startDate != ''">
                AND create_time >= #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                AND create_time &lt;= #{params.endDate}
            </if>
        </where>
        ORDER BY is_top DESC, sort ASC, create_time DESC
    </select>
    
</mapper> 