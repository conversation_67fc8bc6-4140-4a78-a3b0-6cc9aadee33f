package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.SysJobLog;
import com.admin.service.SysJobLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/joblog")
public class SysJobLogController {

    @Autowired
    private SysJobLogService jobLogService;
    @Log(title = "任务日志", operType = "查询")
    @GetMapping("/list")
    public R list(
                @RequestParam(required = false) String jobName,
                 @RequestParam(required = false) String jobType,
                 @RequestParam(required = false) String executionResult,
                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime startDate,
                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime endDate,
                 @RequestParam(defaultValue = "1") Integer page,
                 @RequestParam(defaultValue = "10") Integer limit) {
        
        IPage<SysJobLog> pageData = jobLogService.getJobLogList(jobName,
            jobType, executionResult, startDate, endDate, page, limit);
            
        return R.ok()
                .put("data", pageData.getRecords())
                .put("total", pageData.getTotal());
    }
    @Log(title = "任务日志", operType = "清空")
    @DeleteMapping("/clean")
    public R clean() {
        return jobLogService.cleanJobLog() ? R.ok() : R.error("清空失败");
    }
} 