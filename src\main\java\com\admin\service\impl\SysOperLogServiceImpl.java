package com.admin.service.impl;

import com.admin.entity.SysOperLog;
import com.admin.mapper.SysOperLogMapper;
import com.admin.service.SysOperLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.Map;

@Service
public class SysOperLogServiceImpl extends ServiceImpl<SysOperLogMapper, SysOperLog> implements SysOperLogService {

    @Override
    public IPage<SysOperLog> getOperLogPage(Map<String, Object> params) {
        int pageNum = params.get("page") == null ? 1 : Integer.parseInt(params.get("page").toString());
        int pageSize = params.get("limit") == null ? 10 : Integer.parseInt(params.get("limit").toString());
        
        Page<SysOperLog> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectOperLogPage(page, params);
    }

    @Override
    public boolean cleanOperLog() {
        return baseMapper.delete(null) > 0;
    }
} 