package com.admin.service.impl;

import com.admin.entity.SysLoginLog;
import com.admin.mapper.SysLoginLogMapper;
import com.admin.service.SysLoginLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;

@Service
public class SysLoginLogServiceImpl extends ServiceImpl<SysLoginLogMapper, SysLoginLog> implements SysLoginLogService {

    @Override
    public IPage<SysLoginLog> getLoginLogList(String username, String ip, Integer status,
                                            LocalDateTime startDate, LocalDateTime endDate, 
                                            Integer page, Integer limit) {
        LambdaQueryWrapper<SysLoginLog> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        wrapper.like(StringUtils.isNotBlank(username), SysLoginLog::getUsername, username)
               .like(StringUtils.isNotBlank(ip), SysLoginLog::getIpaddr, ip)
               .eq(status != null, SysLoginLog::getStatus, status)
               .ge(startDate != null, SysLoginLog::getLoginTime, startDate)
               .le(endDate != null, SysLoginLog::getLoginTime, endDate)
               .orderByDesc(SysLoginLog::getLoginTime);

        return page(new Page<>(page, limit), wrapper);
    }

    @Override
    public boolean cleanLoginLog() {
        return remove(new LambdaQueryWrapper<>());
    }
} 