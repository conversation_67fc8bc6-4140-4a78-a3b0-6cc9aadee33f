<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.SysUserMapper">
    <select id="selectUserPage" resultType="com.admin.entity.SysUser">
        SELECT 
            u.*,
            r.role_key as role,
            r.role_name as roleName
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id
        <where>
            <if test="username != null and username != ''">
                AND u.username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="status != null">
                AND u.status = #{status}
            </if>
        </where>
        ORDER BY u.create_time DESC
    </select>
    
    <select id="selectUserWithRole" resultType="com.admin.entity.SysUser">
        SELECT 
            u.*,
            r.role_key as role,
            r.role_name as roleName
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id
        WHERE u.id = #{id}
    </select>
    
    <select id="selectAllUserEmails" resultType="java.util.Map">
        SELECT 
            id, 
            username, 
            email 
        FROM sys_user 
        WHERE email IS NOT NULL 
        AND email != ''
    </select>
    
    <select id="selectByEmail" resultType="com.admin.entity.SysUser">
        SELECT * 
        FROM sys_user 
        WHERE email = #{email}
    </select>
    
    <select id="countByEmail" resultType="int">
        SELECT COUNT(*) 
        FROM sys_user 
        WHERE email = #{email}
    </select>
</mapper> 