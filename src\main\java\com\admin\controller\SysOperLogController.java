package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.SysOperLog;
import com.admin.service.SysOperLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/operlog")
public class SysOperLogController {

    @Autowired
    private SysOperLogService operLogService;
    @Log(title = "操作日志", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        try {
            IPage<SysOperLog> page = operLogService.getOperLogPage(params);
            return R.ok()
                    .put("data", page.getRecords())
                    .put("total", page.getTotal());
        } catch (Exception e) {
            log.error("查询操作日志失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }
    @Log(title = "操作日志", operType = "清空")
    @DeleteMapping("/clean")
    public R clean() {
        try {
            boolean success = operLogService.cleanOperLog();
            return success ? R.ok() : R.error("清空失败");
        } catch (Exception e) {
            log.error("清空操作日志失败", e);
            return R.error("清空失败：" + e.getMessage());
        }
    }
} 