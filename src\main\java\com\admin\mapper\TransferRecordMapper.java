package com.admin.mapper;

import com.admin.entity.TransferRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Map;
import com.admin.entity.excel.TransferRecordExcel;
import java.util.List;

@Mapper
public interface TransferRecordMapper extends BaseMapper<TransferRecord> {
    
    /**
     * 分页查询转账记录
     */
    IPage<TransferRecord> selectTransferRecordList(Page<TransferRecord> page, @Param("params") Map<String, Object> params);
    
    /**
     * 查询转账统计信息
     */
    Map<String, Object> selectTransferStatistics();
    
    /**
     * 查询转账记录(导出用)
     */
    List<TransferRecordExcel> selectTransferRecordForExport(
        @Param("fromUsername") String fromUsername,
        @Param("toUsername") String toUsername,
        @Param("status") Integer status,
        @Param("startDate") String startDate,
        @Param("endDate") String endDate
    );
} 