package com.admin.service.impl;

import com.admin.mapper.DashboardMapper;
import com.admin.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class DashboardServiceImpl implements DashboardService {
    
    @Autowired
    private DashboardMapper dashboardMapper;
    
    @Override
    public Map<String, Object> getDashboardStats() {
        return dashboardMapper.getDashboardStats();
    }
    

}