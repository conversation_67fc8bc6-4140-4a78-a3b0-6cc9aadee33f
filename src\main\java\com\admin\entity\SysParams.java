package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 系统参数配置
 */
@Data
@TableName("sys_params")
public class SysParams {
    
    /**
     * 参数ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 最低转账限额
     */
    private BigDecimal minTransfer;
    
    /**
     * 最高转账限额
     */
    private BigDecimal maxTransfer;
    
    /**
     * 转账手续费
     */
    private BigDecimal transferFee;
    
    /**
     * 是否允许转账(0:禁用,1:启用)
     */
    private Integer enableTransfer;
    
    /**
     * 最低提现限额
     */
    private BigDecimal minWithdraw;
    
    /**
     * 最高提现限额
     */
    private BigDecimal maxWithdraw;
    
    /**
     * 提现最高自动转账
     */
    private BigDecimal maxAutoWithdraw;
    
    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;
    
    /**
     * 是否允许提现(0:禁用,1:启用)
     */
    private Integer enableWithdraw;
    
    /**
     * 是否自动提现 0-关闭 1-开启
     */
    private Integer autoWithdraw;
    
    /**
     * 是否允许内部转账 0-关闭 1-开启
     */
    private Integer enableInternalTransfer;
    
    /**
     * 最低跟单额度
     */
    private BigDecimal minCopyTrade;
    
    /**
     * 交易盈利比例
     */
    private BigDecimal tradeProfitRate;
    
    /**
     * 每次跟单的跟单账户比例
     */
    private BigDecimal copyTradeAccountRate;
    
    /**
     * 每笔跟单手续费比例
     */
    private BigDecimal copyTradeFee;
    
    /**
     * 平台预留手续费比例
     */
    private BigDecimal platformFeeRate;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 