package com.admin.job;

import com.admin.entity.SysJob;
import com.admin.service.JobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class ScheduleManager {
    
    private static final Logger log = LoggerFactory.getLogger(ScheduleManager.class);
    
    @Autowired
    private ThreadPoolTaskScheduler taskScheduler;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private JobService jobService;
    
    private final Map<Long, ScheduledFuture<?>> jobFutures = new ConcurrentHashMap<>();
    private final Map<Long, Boolean> jobRunningStatus = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        // 初始化时启动所有状态为正常的任务
        log.info("开始初始化定时任务...");
        List<SysJob> jobs = jobService.selectJobsByStatus(1);
        
        if (jobs != null && !jobs.isEmpty()) {
            log.info("找到{}个需要启动的任务", jobs.size());
            for (SysJob job : jobs) {
                try {
                    String[] invokeTarget = job.getInvokeTarget().split("\\.");
                    startJob(job.getId(), 
                        invokeTarget[0],
                        invokeTarget[1],
                        job.getCronExpression());
                    log.info("任务启动成功 - ID:{}, 名称:{}, Cron:{}", 
                        job.getId(), job.getJobName(), job.getCronExpression());
                } catch (Exception e) {
                    log.error("任务启动失败 - ID:" + job.getId(), e);
                }
            }
        } else {
            log.info("没有找到需要启动的任务");
        }
    }
    
    /**
     * 启动任务
     */
    public void startJob(Long jobId, String beanName, String methodName, String cronExpression) {
        log.info("开始启动任务 - jobId:{}, bean:{}, method:{}, cron:{}", 
            jobId, beanName, methodName, cronExpression);
            
        Object target = applicationContext.getBean(beanName);
        ScheduledFuture<?> future = taskScheduler.schedule(
            () -> {
                try {
                    jobRunningStatus.put(jobId, true);
                    Method method = target.getClass().getDeclaredMethod(methodName, Long.class);
                    method.invoke(target, jobId);
                } catch (Exception e) {
                    log.error("任务执行异常 - jobId:" + jobId, e);
                } finally {
                    jobRunningStatus.put(jobId, false);
                }
            },
            new CronTrigger(cronExpression)
        );
        
        jobFutures.put(jobId, future);
        log.info("任务启动完成 - jobId:{}", jobId);
    }
    
    /**
     * 停止任务并清理资源
     */
    public void stopJob(Long jobId) {
        // 停止定时任务
        ScheduledFuture<?> future = jobFutures.get(jobId);
        if (future != null) {
            future.cancel(true);
            jobFutures.remove(jobId);
        }
        
        // 清理运行状态
        jobRunningStatus.remove(jobId);
    }
    
    /**
     * 检查任务是否存在
     */
    public boolean exists(Long jobId) {
        return jobFutures.containsKey(jobId);
    }
    
    /**
     * 检查任务是否正在执行
     */
    public boolean isRunning(Long jobId) {
        return jobRunningStatus.getOrDefault(jobId, false);
    }
    
    /**
     * 执行一次任务
     */
    public void runOnce(Long jobId, String beanName, String methodName) {
        Object target = applicationContext.getBean(beanName);
        try {
            jobRunningStatus.put(jobId, true);  // 标记任务开始执行
            Method method = target.getClass().getDeclaredMethod(methodName, Long.class);
            method.invoke(target, jobId);
        } catch (Exception e) {
            log.error("执行任务失败", e);
            throw new RuntimeException("执行任务失败: " + e.getMessage());
        } finally {
            jobRunningStatus.put(jobId, false); // 标记任务执行完成
        }
    }
} 