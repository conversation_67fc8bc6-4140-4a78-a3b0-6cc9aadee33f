package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("sys_oper_log")
public class SysOperLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String title;           // 模块标题
    private String operType;        // 操作类型
    private String method;          // 方法名称
    private String requestMethod;   // 请求方式
    private String operName;        // 操作人员
    private String operUrl;         // 请求URL
    private String operIp;          // 主机地址
    private String operParam;       // 请求参数
    private Integer status;         // 操作状态（0正常 1异常）
    private String errorMsg;        // 错误消息
    private LocalDateTime operTime; // 操作时间
} 