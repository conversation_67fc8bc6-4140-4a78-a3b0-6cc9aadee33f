package com.admin.service.impl;

import com.admin.common.utils.SecurityUtils;
import com.admin.entity.Notice;
import com.admin.mapper.NoticeMapper;
import com.admin.service.NoticeService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.Map;

@Service
public class NoticeServiceImpl extends ServiceImpl<NoticeMapper, Notice> implements NoticeService {

    @Override
    public IPage<Notice> getNoticePage(Map<String, Object> params) {
        int pageNum = params.get("page") == null ? 1 : Integer.parseInt(params.get("page").toString());
        int pageSize = params.get("limit") == null ? 10 : Integer.parseInt(params.get("limit").toString());
        
        Page<Notice> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectNoticePage(page, params);
    }

    @Override
    public boolean updateStatus(Long id, Integer status) {
        // 先获取原记录
        Notice oldNotice = getById(id);
        if (oldNotice == null) {
            return false;
        }
        
        Notice notice = new Notice();
        notice.setId(id);
        notice.setStatus(status);
        notice.setUpdateTime(LocalDateTime.now());
        
        if (status == 1) {
            // 设置为发布状态时，一定要设置发布时间
            notice.setPublishTime(LocalDateTime.now());
        } else {
            // 设置为未发布状态时，清空发布时间
            notice.setPublishTime(null);
        }
        
        return updateById(notice);
    }

    @Override
    public boolean saveOrUpdateNotice(Notice notice) {
        if (notice.getId() == null) {
            // 新增
            notice.setCreateTime(LocalDateTime.now());
            notice.setCreateBy(SecurityUtils.getUsername()); // 从登录用户中获取用户名
            // 如果是发布状态，设置发布时间
            if (notice.getStatus() == 1) {
                notice.setPublishTime(LocalDateTime.now());
            }
        } else {
            // 修改
            Notice oldNotice = getById(notice.getId());
            if (oldNotice != null) {
                // 如果从未发布改为发布，设置发布时间
                if (oldNotice.getStatus() == 0 && notice.getStatus() == 1) {
                    notice.setPublishTime(LocalDateTime.now());
                }
                // 如果从发布改为未发布，清空发布时间
                if (oldNotice.getStatus() == 1 && notice.getStatus() == 0) {
                    notice.setPublishTime(null);
                }
            }
        }
        notice.setUpdateTime(LocalDateTime.now());
        notice.setUpdateBy(SecurityUtils.getUsername()); // 从登录用户中获取用户名
        return saveOrUpdate(notice);
    }
} 