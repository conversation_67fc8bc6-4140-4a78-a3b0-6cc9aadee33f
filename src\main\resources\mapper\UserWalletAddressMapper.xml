<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.UserWalletAddressMapper">
    
    <resultMap type="com.admin.entity.UserWalletAddress" id="UserWalletAddressResult">
        <id property="id" column="id"/>
        <result property="chainName" column="chain_name"/>
        <result property="chainAddress" column="chain_address"/>
        <result property="privateKey" column="private_key"/>
        <result property="bnbBalance" column="bnb_balance"/>
        <result property="usdtBalance" column="usdt_balance"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 新增VO resultMap -->
    <resultMap id="UserWalletAddressVOResult" type="com.admin.vo.UserWalletAddressVO" extends="UserWalletAddressResult">
        <result property="username" column="username"/>
        <result property="email" column="email"/>
    </resultMap>

    <select id="selectWalletPage" resultMap="UserWalletAddressVOResult">
      SELECT
        w.*, u.username, u.email
      FROM user_wallet_address w
      LEFT JOIN front_user u ON w.user_id = u.id
      <where>
        <if test="chainName != null and chainName != ''">
          AND w.chain_name = #{chainName}
        </if>
        <if test="chainAddress != null and chainAddress != ''">
          AND w.chain_address = #{chainAddress}
        </if>
        <if test="username != null and username != ''">
          AND u.username = #{username}
        </if>
        <if test="email != null and email != ''">
          AND u.email = #{email}
        </if>
        <if test="userId != null">
          AND w.user_id = #{userId}
        </if>
        <if test="usdtNotEmpty != null and usdtNotEmpty">
          AND w.usdt_balance > 0
        </if>
        <if test="bnbNotEmpty != null and bnbNotEmpty">
          AND w.bnb_balance > 0
        </if>
      </where>
      ORDER BY w.id DESC
    </select>

    <select id="selectUserWalletAddressPage" resultMap="UserWalletAddressResult">
        select * from user_wallet_address
        <where>
            <if test="wallet.chainName != null and wallet.chainName != ''">
                AND chain_name like concat('%', #{wallet.chainName}, '%')
            </if>
            <if test="wallet.chainAddress != null and wallet.chainAddress != ''">
                AND chain_address like concat('%', #{wallet.chainAddress}, '%')
            </if>
            <if test="wallet.userId != null">
                AND user_id = #{wallet.userId}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWalletDetailById" resultMap="UserWalletAddressVOResult">
      SELECT w.*, u.username, u.email
      FROM user_wallet_address w
      LEFT JOIN front_user u ON w.user_id = u.id
      WHERE w.id = #{id}
    </select>

    <select id="sumUsdtAndBnb" resultType="java.util.Map">
      SELECT
        COALESCE(SUM(w.usdt_balance),0) AS totalUsdt,
        COALESCE(SUM(w.bnb_balance),0) AS totalBnb
      FROM user_wallet_address w
      LEFT JOIN front_user u ON w.user_id = u.id
      <where>
        <if test="chainName != null and chainName != ''">
          AND w.chain_name = #{chainName}
        </if>
        <if test="chainAddress != null and chainAddress != ''">
          AND w.chain_address = #{chainAddress}
        </if>
        <if test="username != null and username != ''">
          AND u.username = #{username}
        </if>
        <if test="email != null and email != ''">
          AND u.email = #{email}
        </if>
        <if test="userId != null">
          AND w.user_id = #{userId}
        </if>
        <if test="usdtNotEmpty != null and usdtNotEmpty">
          AND w.usdt_balance > 0
        </if>
        <if test="bnbNotEmpty != null and bnbNotEmpty">
          AND w.bnb_balance > 0
        </if>
      </where>
    </select>

    <select id="findByAddress" resultMap="UserWalletAddressResult">
      SELECT * FROM user_wallet_address WHERE chain_address = #{chainAddress}
    </select>
    
</mapper> 