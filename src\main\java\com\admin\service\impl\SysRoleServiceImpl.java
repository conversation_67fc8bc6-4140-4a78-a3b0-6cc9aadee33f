package com.admin.service.impl;

import com.admin.entity.SysRole;
import com.admin.entity.SysRoleMenu;
import com.admin.mapper.SysRoleMapper;
import com.admin.mapper.SysRoleMenuMapper;
import com.admin.service.SysRoleService;
import com.admin.common.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Override
    public List<SysRole> getRoleList(String name) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(name)) {
            wrapper.like(SysRole::getRoleName, name);
        }
        wrapper.orderByAsc(SysRole::getRoleSort);
        return baseMapper.selectList(wrapper);
    }

    @Override
    @Transactional
    public void addRole(SysRole role) {
        // 检查角色名称是否存在
        if (isRoleNameExist(role.getRoleName(), null)) {
            throw new BusinessException("角色名称已存在");
        }
        // 检查角色编码是否存在
        if (isRoleKeyExist(role.getRoleKey(), null)) {
            throw new BusinessException("角色编码已存在");
        }
        
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        baseMapper.insert(role);
    }

    @Override
    @Transactional
    public void updateRole(SysRole role) {
        // 检查角色是否存在
        SysRole existRole = baseMapper.selectById(role.getId());
        if (existRole == null) {
            throw new BusinessException("角色不存在");
        }
        
        // 检查角色名称是否重复
        if (!existRole.getRoleName().equals(role.getRoleName()) && isRoleNameExist(role.getRoleName(), role.getId())) {
            throw new BusinessException("角色名称已存在");
        }
        
        // 检查角色编码是否重复
        if (!existRole.getRoleKey().equals(role.getRoleKey()) && isRoleKeyExist(role.getRoleKey(), role.getId())) {
            throw new BusinessException("角色编码已存在");
        }
        
        role.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(role);
    }

    @Override
    @Transactional
    public void deleteRole(Long id) {
        // 检查是否有用户关联此角色
        int count = baseMapper.countUsersByRoleId(id);
        if (count > 0) {
            throw new BusinessException("该角色已被分配给用户，无法删除");
        }
        
        // 删除角色权限关系
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, id);
        roleMenuMapper.delete(wrapper);
        
        // 删除角色
        baseMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void updateRoleStatus(Long id, Integer status) {
        SysRole role = new SysRole();
        role.setId(id);
        role.setStatus(status);
        role.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(role);
    }

    @Override
    @Transactional
    public void assignPermissions(Long roleId, List<Long> menuIds) {
        // 先删除原有权限
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, roleId);
        roleMenuMapper.delete(wrapper);
        
        // 添加新权限
        if (menuIds != null && !menuIds.isEmpty()) {
            List<SysRoleMenu> roleMenus = menuIds.stream().map(menuId -> {
                SysRoleMenu roleMenu = new SysRoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(menuId);
                return roleMenu;
            }).collect(Collectors.toList());
            
            roleMenus.forEach(roleMenuMapper::insert);
        }
    }

    /**
     * 检查角色名称是否存在
     */
    private boolean isRoleNameExist(String roleName, Long excludeId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleName, roleName);
        if (excludeId != null) {
            wrapper.ne(SysRole::getId, excludeId);
        }
        return baseMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查角色编码是否存在
     */
    private boolean isRoleKeyExist(String roleKey, Long excludeId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleKey, roleKey);
        if (excludeId != null) {
            wrapper.ne(SysRole::getId, excludeId);
        }
        return baseMapper.selectCount(wrapper) > 0;
    }

    
} 