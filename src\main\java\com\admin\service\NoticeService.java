package com.admin.service;

import com.admin.entity.Notice;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.Map;

public interface NoticeService extends IService<Notice> {
    // 分页查询公告列表
    IPage<Notice> getNoticePage(Map<String, Object> params);
    
    /**
     * 更新公告状态
     * @param id 公告ID
     * @param status 状态(0:未发布,1:发布)
     * @return 更新结果
     */
    boolean updateStatus(Long id, Integer status);
    
    // 新增或修改公告
    boolean saveOrUpdateNotice(Notice notice);
} 