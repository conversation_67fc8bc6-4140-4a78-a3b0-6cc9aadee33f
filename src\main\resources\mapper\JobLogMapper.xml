<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.JobLogMapper">
    <!-- 根据任务ID查询日志列表 -->
    <select id="selectJobLogList" resultType="com.admin.entity.JobLog">
        SELECT 
            id,
            job_id,
            job_name,
            job_type,
            execution_time,
            execution_duration,
            execution_result,
            execution_message,
            create_time
        FROM sys_job_log
        <where>
            <if test="jobId != null">
                AND job_id = #{jobId}
            </if>
        </where>
        ORDER BY execution_time DESC
    </select>
</mapper> 