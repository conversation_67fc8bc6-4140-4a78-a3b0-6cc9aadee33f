package com.admin.service;

import com.admin.entity.UserWalletAddress;
import com.admin.vo.UserWalletAddressVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface IUserWalletAddressService extends IService<UserWalletAddress> {
    
    /**
     * 分页查询用户钱包地址列表
     */
    Page<UserWalletAddress> selectUserWalletAddressPage(Page<UserWalletAddress> page, UserWalletAddress wallet);

    /**
     * 分页查询用户钱包地址列表（带用户名和手机号，全部精确查询）
     */
    IPage<UserWalletAddressVO> selectWalletPage(Page<?> page, String chainName, String chainAddress, String username, String email, Long userId, Boolean usdtNotEmpty, Boolean bnbNotEmpty);

    /**
     * 根据ID查询钱包详情（带用户名和手机号）
     */
    UserWalletAddressVO selectWalletDetailById(Long id);

    /**
     * 统计USDT和BNB总和（可带条件）
     */
    java.util.Map<String, Object> sumUsdtAndBnb(String chainName, String chainAddress, String username, String email, Long userId, Boolean usdtNotEmpty, Boolean bnbNotEmpty);
} 