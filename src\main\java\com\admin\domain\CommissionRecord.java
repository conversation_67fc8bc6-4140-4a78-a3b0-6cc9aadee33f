package com.admin.domain;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CommissionRecord {
    private Long id;
    private Long userId;
    private String username;
    private String phone;
    private Integer commissionType; // 1.购买赠送 2.推广赠送 3.培育赠送 4.管理赠送
    private BigDecimal commissionAmount;
    private Integer releaseStatus;
    private Date releaseTime;
    private String remark;
    private Date createTime;
    private Date updateTime;
} 