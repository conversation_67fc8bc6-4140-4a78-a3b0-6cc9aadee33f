package com.admin.service;

import com.admin.entity.WithdrawRecord;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface WithdrawRecordService extends IService<WithdrawRecord> {
    
    /**
     * 分页查询提现记录
     */
    IPage<WithdrawRecord> getWithdrawRecordList(Map<String, Object> params);
    
    /**
     * 查询提现统计信息
     */
    Map<String, Object> getWithdrawStatistics();
    
    /**
     * 审核提现申请
     * @param id 记录ID
     * @param status 审核状态
     * @param remark 审核备注
     * @return 处理结果
     */
    boolean auditWithdraw(Long id, Integer status, String remark);
    
    /**
     * 导出提现记录
     */
    List<WithdrawRecord> exportWithdrawRecord(String username, String status, String startDate, String endDate);
} 