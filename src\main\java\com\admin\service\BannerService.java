package com.admin.service;

import com.admin.entity.Banner;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

public interface BannerService extends IService<Banner> {
    IPage<Banner> getBannerList(String title, Integer status, Integer page, Integer limit);
    
    boolean addBanner(Banner banner);
    
    boolean updateBanner(Banner banner);
    
    boolean toggleStatus(Long id, Integer status);
    
    boolean deleteBanner(Long id);
} 