package com.admin.service.impl;

import com.admin.entity.Agreement;
import com.admin.mapper.AgreementMapper;
import com.admin.service.AgreementService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;

@Service
public class AgreementServiceImpl extends ServiceImpl<AgreementMapper, Agreement> implements AgreementService {

    @Override
    public IPage<Agreement> getAgreementList(String title, Integer page, Integer limit) {
        LambdaQueryWrapper<Agreement> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(title), Agreement::getTitle, title)
               .orderByDesc(Agreement::getCreateTime);
               
        return page(new Page<>(page, limit), wrapper);
    }

    @Override
    public boolean addAgreement(Agreement agreement) {
        agreement.setCreateTime(LocalDateTime.now());
        agreement.setCreateBy("admin"); // 这里应该从登录用户中获取
        agreement.setStatus(1);
        return save(agreement);
    }

    @Override
    public boolean updateAgreement(Agreement agreement) {
        agreement.setUpdateTime(LocalDateTime.now());
        agreement.setUpdateBy("admin"); // 这里应该从登录用户中获取
        return updateById(agreement);
    }

    @Override
    public boolean deleteAgreement(Long id) {
        return removeById(id);
    }
} 