package com.admin.mapper;

import com.admin.entity.AccountTransferRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Map;

@Mapper
public interface AccountTransferRecordMapper extends BaseMapper<AccountTransferRecord> {
    IPage<AccountTransferRecord> selectAccountTransferRecordList(Page<AccountTransferRecord> page, @Param("params") Map<String, Object> params);
} 