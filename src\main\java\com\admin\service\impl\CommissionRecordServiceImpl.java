package com.admin.service.impl;

import com.admin.entity.CommissionRecord;
import com.admin.mapper.CommissionRecordMapper;
import com.admin.service.CommissionRecordService;
import com.admin.vo.CommissionStats;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;
import java.math.BigDecimal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class CommissionRecordServiceImpl extends ServiceImpl<CommissionRecordMapper, CommissionRecord> 
    implements CommissionRecordService {

    private static final Logger log = LoggerFactory.getLogger(CommissionRecordServiceImpl.class);

    @Override
    public IPage<CommissionRecord> getCommissionList(String keyword, String commissionType,
                                                   LocalDateTime startDate, LocalDateTime endDate,
                                                   Integer page, Integer limit) {
        LambdaQueryWrapper<CommissionRecord> wrapper = new LambdaQueryWrapper<>();
        
        wrapper.and(StringUtils.isNotBlank(keyword), w -> w
                .like(CommissionRecord::getUsername, keyword)
                .or()
                .like(CommissionRecord::getPhone, keyword))
               .eq(StringUtils.isNotBlank(commissionType), CommissionRecord::getCommissionType, commissionType)
               .ge(startDate != null, CommissionRecord::getCreateTime, startDate)
               .le(endDate != null, CommissionRecord::getCreateTime, endDate)
               .orderByDesc(CommissionRecord::getCreateTime);

        return page(new Page<>(page, limit), wrapper);
    }

    @Override
    public CommissionStats getCommissionStats(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return baseMapper.getCommissionStats(startTime, endTime);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            // 返回空统计数据而不是 null
            CommissionStats emptyStats = new CommissionStats();
            emptyStats.setCount(0);
            emptyStats.setTotalAmount(BigDecimal.ZERO);
            return emptyStats;
        }
    }

    public List<CommissionRecord> getExportList(String keyword, String commissionType,
                                              LocalDateTime startDate, LocalDateTime endDate) {
        LambdaQueryWrapper<CommissionRecord> wrapper = new LambdaQueryWrapper<>();
        
        wrapper.and(StringUtils.isNotBlank(keyword), w -> w
                .like(CommissionRecord::getUsername, keyword)
                .or()
                .like(CommissionRecord::getPhone, keyword))
               .eq(StringUtils.isNotBlank(commissionType), CommissionRecord::getCommissionType, commissionType)
               .ge(startDate != null, CommissionRecord::getCreateTime, startDate)
               .le(endDate != null, CommissionRecord::getCreateTime, endDate)
               .orderByDesc(CommissionRecord::getCreateTime);
        
        return list(wrapper);
    }

    @Override
    public BigDecimal calculateFilteredTotal(String keyword, String commissionType, 
                                           LocalDateTime startDate, LocalDateTime endDate) {
        LambdaQueryWrapper<CommissionRecord> wrapper = new LambdaQueryWrapper<>();
        
        // 添加关键字搜索条件
        if (StringUtils.isNotBlank(keyword)) {
            wrapper.and(w -> w
                .like(CommissionRecord::getUsername, keyword)
                .or()
                .like(CommissionRecord::getPhone, keyword)
            );
        }
        
        // 添加赠送类型条件
        if (StringUtils.isNotBlank(commissionType)) {
            wrapper.eq(CommissionRecord::getCommissionType, commissionType);
        }
        
        // 添加时间范围条件
        if (startDate != null) {
            wrapper.ge(CommissionRecord::getReleaseTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(CommissionRecord::getReleaseTime, endDate);
        }
        
        // 只统计已赠送的记录
        wrapper.eq(CommissionRecord::getReleaseStatus, 1);
        
        // 使用 SELECT SUM 查询总金额
        return baseMapper.selectList(wrapper)
            .stream()
            .map(CommissionRecord::getCommissionAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
} 