package com.admin.controller;

import com.admin.common.utils.R;
import com.admin.dto.LoginDTO;
import com.admin.dto.ResetPasswordDTO;
import com.admin.service.AuthService;
import com.admin.vo.CaptchaVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.Email;

@RestController
@RequestMapping("/auth")
@CrossOrigin
@Slf4j
@Validated
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    /**
     * 获取验证码
     */
    @GetMapping("/captcha")
    public R getCaptcha() {
        CaptchaVO captcha = authService.generateCaptcha();
        return R.ok().data(captcha);
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public R login(@Validated @RequestBody LoginDTO loginDTO) {
        try {
            String token = authService.login(loginDTO);
            return R.ok().put("token", token);
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            if (errorMsg != null) {
                if (errorMsg.contains("验证码")) {
                    return R.error("验证码不正确");
                } else if (errorMsg.contains("用户名") || errorMsg.contains("密码")) {
                    return R.error("用户名或密码不正确");
                }
            }
            log.error("登录异常", e);
            return R.error("登录失败，请稍后重试");
        }
    }
    
    /**
     * 发送重置密码验证码
     */
    @PostMapping("/password/reset/code")
    public R sendResetCode(
            @RequestParam(required = true, defaultValue = "") 
            @Email(message = "邮箱格式不正确") 
            String email) {
        if (email.isEmpty()) {
            return R.error("邮箱不能为空");
        }
        authService.sendResetCode(email);
        return R.ok();
    }
    
    /**
     * 重置密码
     */
    @PostMapping("/password/reset")
    public R resetPassword(@Validated @RequestBody ResetPasswordDTO resetPasswordDTO) {
        authService.resetPassword(resetPasswordDTO);
        return R.ok();
    }
} 