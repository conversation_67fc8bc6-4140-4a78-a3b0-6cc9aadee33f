package com.admin.controller;

import com.admin.common.utils.R;
import com.admin.common.utils.SecurityUtils;
import com.admin.service.MenuService;
import com.admin.vo.MenuVO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/menu")
public class MenuController {

    @Autowired
    private MenuService menuService;

    @GetMapping("/nav")
    public R getNavMenus() {
        Long userId = SecurityUtils.getUserId();
        log.info("Getting menus for user: {}", userId);
        List<MenuVO> menus = menuService.getUserMenus(userId);
        log.info("Found {} menus", menus.size());
        return R.ok().data(menus);
    }

    // 获取菜单树形结构
    @GetMapping("/tree")
    public R getMenuTree() {
        List<MenuVO> menuTree = menuService.getMenuTree();
        return R.ok().data(menuTree);
    }

    // 获取角色已分配的菜单ID列表
    @GetMapping("/role/{roleId}")
    public R getRoleMenuIds(@PathVariable Long roleId) {
        List<Long> menuIds = menuService.getRoleMenuIds(roleId);
        return R.ok().data(menuIds);
    }

    // 保存角色菜单权限
    @PostMapping("/role/{roleId}")
    public R assignRoleMenu(@PathVariable Long roleId, @RequestBody List<Long> menuIds) {
        menuService.assignRoleMenu(roleId, menuIds);
        return R.ok();
    }
} 