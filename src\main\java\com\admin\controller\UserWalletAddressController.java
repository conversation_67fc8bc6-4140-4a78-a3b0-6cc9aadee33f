package com.admin.controller;

import com.admin.common.utils.R;
import com.admin.entity.UserWalletAddress;
import com.admin.vo.UserWalletAddressVO;
import com.admin.service.IUserWalletAddressService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/user/wallet")
public class UserWalletAddressController {
    
    @Autowired
    private IUserWalletAddressService userWalletAddressService;
    
    /**
     * 获取钱包地址列表（精确查询，带用户名和手机号）
     */
    @GetMapping("/list")
    public R list(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                 @RequestParam(value = "chainName", required = false) String chainName,
                 @RequestParam(value = "chainAddress", required = false) String chainAddress,
                 @RequestParam(value = "username", required = false) String username,
                 @RequestParam(value = "email", required = false) String email,
                 @RequestParam(value = "userId", required = false) Long userId,
                 @RequestParam(value = "usdtNotEmpty", required = false) Boolean usdtNotEmpty,
                 @RequestParam(value = "bnbNotEmpty", required = false) Boolean bnbNotEmpty) {
        Page<UserWalletAddressVO> page = new Page<>(pageNum, pageSize);
        IPage<UserWalletAddressVO> list = userWalletAddressService.selectWalletPage(page, chainName, chainAddress, username, email, userId, usdtNotEmpty, bnbNotEmpty);
        java.util.Map<String, Object> sum = userWalletAddressService.sumUsdtAndBnb(chainName, chainAddress, username, email, userId, usdtNotEmpty, bnbNotEmpty);
        return R.ok().put("data", list)
                     .put("totalUsdt", sum.getOrDefault("totalUsdt", 0))
                     .put("totalBnb", sum.getOrDefault("totalBnb", 0));
    }
    
    /**
     * 获取钱包地址详细信息
     */
    @GetMapping("/{id}")
    public R getInfo(@PathVariable("id") Long id) {
        return R.ok().put("data", userWalletAddressService.getById(id));
    }
    
    /**
     * 获取钱包地址详细信息（带用户名和手机号）
     */
    @GetMapping("/detail/{id}")
    public R getDetail(@PathVariable("id") Long id) {
        return R.ok().put("data", userWalletAddressService.selectWalletDetailById(id));
    }
    
    /**
     * 新增钱包地址
     */
    @PostMapping
    public R add(@RequestBody UserWalletAddress wallet) {
        return userWalletAddressService.save(wallet) ? R.ok() : R.error("添加失败");
    }
    
    /**
     * 修改钱包地址
     */
    @PutMapping
    public R update(@RequestBody UserWalletAddress wallet) {
        return userWalletAddressService.updateById(wallet) ? R.ok() : R.error("修改失败");
    }
    
    /**
     * 删除钱包地址
     */
    @DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids) {
        return userWalletAddressService.removeByIds(java.util.Arrays.asList(ids)) ? R.ok() : R.error("删除失败");
    }
} 