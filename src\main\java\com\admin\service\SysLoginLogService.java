package com.admin.service;

import com.admin.entity.SysLoginLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.time.LocalDateTime;

public interface SysLoginLogService extends IService<SysLoginLog> {
    IPage<SysLoginLog> getLoginLogList(String username, String ip, Integer status, 
                                     LocalDateTime startDate, LocalDateTime endDate, 
                                     Integer page, Integer limit);
    
    boolean cleanLoginLog();
} 