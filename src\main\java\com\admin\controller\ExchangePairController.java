package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.util.R;
import com.admin.entity.ExchangePairInfo;
import com.admin.service.ExchangePairService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
 
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * 交易对管理Controller
 */
@RestController
@RequestMapping("/exchange/pair")
public class ExchangePairController {
    @Autowired
    private ExchangePairService exchangePairService;

    /**
     * 查询交易对列表
     */
    @Log(title = "交易对管理", operType = "查询")
 
    @GetMapping("/list")
    public R list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String exchangeName,
            @RequestParam(required = false) String pairName,
            @RequestParam(required = false) Integer specialProcessing,
            @RequestParam(required = false) Integer isEnabled
    ) {
        IPage<ExchangePairInfo> page = exchangePairService.getExchangePairPage(pageNum, pageSize, exchangeName, pairName, specialProcessing, isEnabled);
        return R.ok().put("data", new HashMap<String, Object>() {{
            put("records", page.getRecords());
            put("total", page.getTotal());
        }});
    }

    /**
     * 获取交易对详细信息
     */
 
    @GetMapping(value = "/{id}")
    public R<ExchangePairInfo> getInfo(@PathVariable("id") Long id) {
        return R.ok(exchangePairService.selectExchangePairById(id));
    }

    /**
     * 新增交易对
     */
   
    @PostMapping
    public R<Void> add(@RequestBody ExchangePairInfo exchangePair) {
        return exchangePairService.insertExchangePair(exchangePair) > 0 ? R.ok() : R.fail();
    }

    /**
     * 修改交易对
     */
  
    @PutMapping
    public R<Void> edit(@RequestBody ExchangePairInfo exchangePair) {
        return exchangePairService.updateExchangePair(exchangePair) > 0 ? R.ok() : R.fail();
    }

    /**
     * 删除交易对
     */
   
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return exchangePairService.deleteExchangePairByIds(ids) > 0 ? R.ok() : R.fail();
    }

    /**
     * 修改交易对状态
     */
 
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody ExchangePairInfo exchangePair) {
        return exchangePairService.updateExchangePairStatus(exchangePair) > 0 ? R.ok() : R.fail();
    }

    /**
     * 获取所有启用的交易对名称
     */
    @GetMapping("/enabledList")
    public R enabledPairList() {
        return R.ok().put("data", exchangePairService.getEnabledPairNames());
    }
} 