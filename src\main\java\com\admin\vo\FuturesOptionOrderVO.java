package com.admin.vo;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FuturesOptionOrderVO {
    private Long id;
    private Long userId;
    private String symbol;
    private BigDecimal marginAmount;
    private BigDecimal positionAmount;
    private Integer lever;
    private Integer direction;
    private BigDecimal takeProfit;
    private BigDecimal stopLoss;
    private BigDecimal openPrice;
    private BigDecimal closePrice;
    private Date openTime;
    private Date closeTime;
    private Integer status;
    private BigDecimal profit;
    private Integer profitStatus;
    private Integer isSettlement;
    private Date createTime;
    private Date updateTime;
    // 可选：用户信息
    private String userAvatar;
    private String userEmail;
    private String userNo;
} 