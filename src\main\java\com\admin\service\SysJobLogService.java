package com.admin.service;

import com.admin.entity.SysJobLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.time.LocalDateTime;

public interface SysJobLogService extends IService<SysJobLog> {
    IPage<SysJobLog> getJobLogList(String jobName,String jobType, String executionResult,
                                  LocalDateTime startDate, LocalDateTime endDate,
                                  Integer page, Integer limit);
    
    boolean cleanJobLog();
} 