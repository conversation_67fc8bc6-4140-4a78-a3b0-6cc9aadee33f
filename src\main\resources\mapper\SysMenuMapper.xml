<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.SysMenuMapper">
    
    <select id="selectMenusByUserId" resultType="com.admin.entity.SysMenu">
        SELECT DISTINCT m.*
        FROM sys_menu m
        LEFT JOIN sys_role_menu rm ON m.id = rm.menu_id
        LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND m.status = 1
        AND m.visible = 1
        AND m.menu_type IN ('M', 'C')
        ORDER BY m.parent_id, m.order_num
    </select>
    
</mapper> 