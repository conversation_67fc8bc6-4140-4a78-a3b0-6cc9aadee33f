package com.admin.common;

import lombok.Data;
import java.util.HashMap;
import java.util.Map;

@Data
public class Result {
    private Integer code;
    private String msg;
    private Map<String, Object> data = new HashMap<>();
    
    public Result data(String key, Object value) {
        this.data.put(key, value);
        return this;
    }
    
    public static Result ok() {
        Result r = new Result();
        r.setCode(0);
        r.setMsg("success");
        return r;
    }
    
    public static Result error(String msg) {
        Result r = new Result();
        r.setCode(500);
        r.setMsg(msg);
        return r;
    }
} 