package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.Notice;
import com.admin.service.NoticeService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/notice")
public class NoticeController {

    @Autowired
    private NoticeService noticeService;

    /**
     * 获取公告列表
     */
    @Log(title = "公告列表", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        try {
            IPage<Notice> page = noticeService.getNoticePage(params);
            return R.ok()
                    .put("data", page.getRecords())
                    .put("total", page.getTotal());
        } catch (Exception e) {
            log.error("查询公告列表失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取公告详情
     */
    @Log(title = "公告列表", operType = "查询")
    @GetMapping("/{id}")
    public R detail(@PathVariable Long id) {
        try {
            Notice notice = noticeService.getById(id);
            return R.ok().put("data", notice);
        } catch (Exception e) {
            log.error("查询公告详情失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增或修改公告
     */
    @Log(title = "公告列表", operType = "新增")
    @PostMapping("/save")
    public R save(@RequestBody Notice notice) {
        try {
            boolean success = noticeService.saveOrUpdateNotice(notice);
            return success ? R.ok() : R.error("操作失败");
        } catch (Exception e) {
            log.error("保存公告失败", e);
            return R.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 更新公告状态
     */
    @Log(title = "公告列表", operType = "修改")
    @PutMapping("/status/{id}")
    public R updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            Notice notice = noticeService.getById(id);
            if (notice == null) {
                return R.error("公告不存在");
            }
            
            boolean success = noticeService.updateStatus(id, status);
            if (success) {
                // 返回更新后的完整记录
                notice = noticeService.getById(id);
                return R.ok().put("data", notice);
            } else {
                return R.error("更新状态失败");
            }
        } catch (Exception e) {
            log.error("更新公告状态失败", e);
            return R.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除公告
     */
    @Log(title = "公告列表", operType = "删除")
    @DeleteMapping("/{id}")
    public R delete(@PathVariable Long id) {
        try {
            boolean success = noticeService.removeById(id);
            return success ? R.ok() : R.error("删除失败");
        } catch (Exception e) {
            log.error("删除公告失败", e);
            return R.error("删除失败：" + e.getMessage());
        }
    }
} 