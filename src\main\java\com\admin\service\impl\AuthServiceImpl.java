package com.admin.service.impl;

import com.admin.common.enums.ResultCode;
import com.admin.common.exception.BusinessException;
import com.admin.common.utils.*;
import com.admin.dto.LoginDTO;
import com.admin.dto.ResetPasswordDTO;
import com.admin.entity.SysCaptcha;
import com.admin.entity.SysLoginLog;
import com.admin.entity.SysUser;
import com.admin.mapper.SysCaptchaMapper;
import com.admin.mapper.SysUserMapper;
import com.admin.mapper.SysLoginLogMapper;
import com.admin.service.AuthService;
import com.admin.vo.CaptchaVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AuthServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements AuthService {
    
    @Autowired
    private SysCaptchaMapper sysCaptchaMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private SysLoginLogMapper sysLoginLogMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private EmailUtils emailUtils;
    
    @Override
    public CaptchaVO generateCaptcha() {
        // 生成4位随机验证码（只包含数字和大写字母）
        String code = RandomStringUtils.random(4, true, true).toUpperCase();
        
        // 生成验证码图片
        String base64 = CaptchaUtils.generateImageBase64(code);
        
        // 保存验证码
        String uuid = UUID.randomUUID().toString();
        SysCaptcha captcha = new SysCaptcha();
        captcha.setUuid(uuid);
        captcha.setCode(code);
        captcha.setExpireTime(LocalDateTime.now().plusMinutes(4));
        sysCaptchaMapper.insert(captcha);
        
        // 返回结果
        CaptchaVO vo = new CaptchaVO();
        vo.setUuid(uuid);
        vo.setImageBase64(base64);  // 确保包含 "data:image/png;base64," 前缀
        return vo;
    }
    
    @Override
    public String login(LoginDTO loginDTO) {
        // 验证验证码
        SysCaptcha captcha = sysCaptchaMapper.selectById(loginDTO.getUuid());
        
        if (captcha == null) {
            throw new BusinessException(ResultCode.CAPTCHA_ERROR);
        }
        
        if (captcha.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException(ResultCode.CAPTCHA_EXPIRED);
        }
        
        if (!captcha.getCode().equals(loginDTO.getCaptcha().toUpperCase())) {
            throw new BusinessException(ResultCode.CAPTCHA_ERROR);
        }
        
        // 验证用户名和密码
        SysUser user = sysUserMapper.selectOne(
            new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, loginDTO.getUsername())
        );
        
        // 记录登录日志
        SysLoginLog loginLog = new SysLoginLog();
        loginLog.setUsername(loginDTO.getUsername());
        loginLog.setIpaddr(IpUtils.getIpAddr());
        loginLog.setLoginLocation(UserAgentUtils.getLoginLocation());
        loginLog.setBrowser(UserAgentUtils.getBrowser());
        loginLog.setOs(UserAgentUtils.getOperatingSystem());
        loginLog.setLoginTime(LocalDateTime.now());
        
        // 用户名不在或密码错误，统一提示
        if (user == null || !passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            loginLog.setStatus(0);
            loginLog.setMsg("用户名或密码错误");
            sysLoginLogMapper.insert(loginLog);
            throw new BusinessException(ResultCode.LOGIN_ERROR);
        }
        
        if (user.getStatus() == 0) {
            loginLog.setStatus(0);
            loginLog.setMsg("账号已被禁用");
            sysLoginLogMapper.insert(loginLog);
            throw new BusinessException(ResultCode.ACCOUNT_DISABLED);
        }
        
        // 登录成功
        loginLog.setStatus(1);
        loginLog.setMsg("登录成功");
        sysLoginLogMapper.insert(loginLog);
        
        // 删除已使用的验证码
        sysCaptchaMapper.deleteById(loginDTO.getUuid());
        
        // 登录成功，生成token
        return JwtUtils.generateToken(user.getId(), loginDTO.getRememberMe());
    }
    
    @Override
    public void sendResetCode(String email) {
        try {
            log.info("开始验证邮箱: {}", email);
            
            // 查询所有用户邮箱
            List<Map<String, Object>> allUsers = sysUserMapper.selectAllUserEmails();
            log.info("有用户: {}", allUsers);
            
            // 计数查询
            int count = sysUserMapper.countByEmail(email.trim());
            log.info("邮箱记录数: {}", count);
            
            // 检查邮箱是否存在
            SysUser user = sysUserMapper.selectByEmail(email.trim());
            log.info("查询结果: {}", user);
            
            if (user == null) {
                throw new BusinessException(ResultCode.EMAIL_NOT_FOUND);
            }
            
            // 生成6位随机验证码
            String resetCode = RandomStringUtils.randomNumeric(6);
            log.info("生成验证码: {}", resetCode);
            
            // 保存验证码到Redis，设置5分钟过期
            String key = "reset_password:" + email;
            redisTemplate.opsForValue().set(key, resetCode, 5, TimeUnit.MINUTES);
            
            // 发送邮件
            emailUtils.sendResetPasswordEmail(email, resetCode);
            
            log.info("验证码发送成功");
        } catch (BusinessException e) {
            log.error("业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("发送重置密码邮件失败: ", e);
            throw new BusinessException("发送证码失败，请稍后试");
        }
    }
    
    @Override
    public void resetPassword(ResetPasswordDTO resetPasswordDTO) {
        // 验证邮箱是否存在
        SysUser user = sysUserMapper.selectOne(
            new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getEmail, resetPasswordDTO.getEmail())
        );
        if (user == null) {
            throw new BusinessException(ResultCode.EMAIL_NOT_FOUND);
        }
        
        // 验证验证码
        String key = "reset_password:" + resetPasswordDTO.getEmail();
        String savedCode = redisTemplate.opsForValue().get(key);
        if (savedCode == null || !savedCode.equals(resetPasswordDTO.getCode())) {
            throw new BusinessException(ResultCode.RESET_CODE_ERROR);
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(resetPasswordDTO.getNewPassword()));
        sysUserMapper.updateById(user);
        
        // 删除验证码
        redisTemplate.delete(key);
    }
    
    @Override
    public SysUser getUserInfo() {
        // 从 token 中获取用户 ID
        Long userId = SecurityUtils.getUserId();
        // 查询用户信息
        SysUser user = sysUserMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        // 除敏感信息
        user.setPassword(null);
        return user;
    }
    
    @Override
    public void updatePassword(SysUser user) {
        sysUserMapper.updateById(user);
    }
} 