package com.admin.mapper;

import com.admin.entity.TradeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Map;
import java.math.BigDecimal;

@Mapper
public interface TradeRecordMapper extends BaseMapper<TradeRecord> {
        
    /**
     * 插入交易记录
     */
    int insertTradeRecord(@Param("userId") Long userId, 
                         @Param("username") String username, 
                         @Param("tradeType") String tradeType, 
                         @Param("amount") BigDecimal amount, 
                         @Param("accountType") Integer accountType, 
                         @Param("remark") String remark);

    IPage<TradeRecord> selectTradeRecordList(Page<TradeRecord> page, @Param("params") Map<String, Object> params);
} 