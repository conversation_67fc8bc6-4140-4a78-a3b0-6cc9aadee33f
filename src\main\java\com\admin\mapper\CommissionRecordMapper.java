package com.admin.mapper;

import com.admin.entity.CommissionRecord;
import com.admin.vo.CommissionStats;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface CommissionRecordMapper extends BaseMapper<CommissionRecord> {
    
    @Select("SELECT COUNT(*) as count, " +
            "COALESCE(SUM(commission_amount), 0) as totalAmount " +
            "FROM commission_record " +
            "WHERE release_status = 1 " +
            "AND release_time <= #{endTime} " +
            "AND (release_time >= #{startTime} OR #{startTime} IS NULL)")
    CommissionStats getCommissionStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 批量插入佣金记录
     */
    int insertBatch(@Param("list") List<CommissionRecord> list);

    int insertCommission(@Param("userId") Long userId, @Param("username") String username, @Param("phone") String phone, @Param("commissionType") Integer commissionType, @Param("commissionAmount") java.math.BigDecimal commissionAmount, @Param("remark") String remark);
} 