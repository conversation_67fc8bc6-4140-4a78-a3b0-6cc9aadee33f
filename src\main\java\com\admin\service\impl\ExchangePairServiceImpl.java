package com.admin.service.impl;

import com.admin.entity.ExchangePairInfo;
import com.admin.mapper.ExchangePairMapper;
import com.admin.service.ExchangePairService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 交易对管理Service业务层处理
 */
@Service
public class ExchangePairServiceImpl implements ExchangePairService {
    @Autowired
    private ExchangePairMapper exchangePairMapper;

    /**
     * 查询交易对列表（分页）
     */
    @Override
    public IPage<ExchangePairInfo> getExchangePairPage(Integer pageNum, Integer pageSize, String exchangeName, String pairName, Integer specialProcessing, Integer isEnabled) {
        return exchangePairMapper.getExchangePairPage(new Page<>(pageNum, pageSize), exchangeName, pairName, specialProcessing, isEnabled);
    }

    /**
     * 查询交易对列表
     */
    @Override
    public List<ExchangePairInfo> selectExchangePairList(ExchangePairInfo exchangePair) {
        return exchangePairMapper.selectExchangePairList(exchangePair);
    }

    /**
     * 查询交易对信息
     */
    @Override
    public ExchangePairInfo selectExchangePairById(Long id) {
        return exchangePairMapper.selectExchangePairById(id);
    }

    /**
     * 新增交易对
     */
    @Override
    public int insertExchangePair(ExchangePairInfo exchangePair) {
        return exchangePairMapper.insertExchangePair(exchangePair);
    }

    /**
     * 修改交易对
     */
    @Override
    public int updateExchangePair(ExchangePairInfo exchangePair) {
        return exchangePairMapper.updateExchangePair(exchangePair);
    }

    /**
     * 批量删除交易对
     */
    @Override
    public int deleteExchangePairByIds(Long[] ids) {
        return exchangePairMapper.deleteExchangePairByIds(ids);
    }

    /**
     * 修改交易对状态
     */
    @Override
    public int updateExchangePairStatus(ExchangePairInfo exchangePair) {
        return exchangePairMapper.updateExchangePair(exchangePair);
    }

    @Override
    public List<String> getEnabledPairNames() {
        return exchangePairMapper.selectEnabledPairNames();
    }
} 