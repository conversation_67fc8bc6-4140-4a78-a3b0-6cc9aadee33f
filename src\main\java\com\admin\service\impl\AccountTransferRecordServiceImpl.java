package com.admin.service.impl;

import com.admin.entity.AccountTransferRecord;
import com.admin.mapper.AccountTransferRecordMapper;
import com.admin.service.AccountTransferRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.Map;

@Service
public class AccountTransferRecordServiceImpl extends ServiceImpl<AccountTransferRecordMapper, AccountTransferRecord> implements AccountTransferRecordService {
    @Override
    public IPage<AccountTransferRecord> pageAccountTransferRecord(Page<AccountTransferRecord> page, Map<String, Object> params) {
        return baseMapper.selectAccountTransferRecordList(page, params);
    }
} 