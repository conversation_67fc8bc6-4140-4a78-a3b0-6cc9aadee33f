package com.admin.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

@Component
public class EmailUtils {
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String from;  // 注入配置文件中的邮箱地址
    
    public void sendResetPasswordEmail(String to, String resetCode) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(from);  // 使用配置文件中的邮箱地址作为发件人
        message.setTo(to);
        message.setSubject("密码重置验证码");
        message.setText("您的密码重置验证码为: " + resetCode + "，有效期5分钟，请勿泄露给他人。");
        mailSender.send(message);
    }
} 