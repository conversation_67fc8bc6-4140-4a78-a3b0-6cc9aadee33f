package com.admin.service.impl;

import com.admin.entity.TransferRecord;
import com.admin.entity.excel.TransferRecordExcel;
import com.admin.mapper.TransferRecordMapper;
import com.admin.service.TransferRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import java.util.List;

@Slf4j
@Service
public class TransferRecordServiceImpl extends ServiceImpl<TransferRecordMapper, TransferRecord> implements TransferRecordService {

    @Override
    public IPage<TransferRecord> getTransferRecordList(Map<String, Object> params) {
        log.info("查询转账记录参数: {}", params);
        
        int pageNum = params.get("page") == null ? 1 : Integer.parseInt(params.get("page").toString());
        int pageSize = params.get("limit") == null ? 10 : Integer.parseInt(params.get("limit").toString());
        
        Page<TransferRecord> page = new Page<>(pageNum, pageSize);
        
        try {
            IPage<TransferRecord> result = baseMapper.selectTransferRecordList(page, params);
            log.info("查询结果: total={}, records={}", result.getTotal(), result.getRecords());
            return result;
        } catch (Exception e) {
            log.error("查询转账记录失败", e);
            throw e;
        }
    }

    @Override
    public Map<String, Object> getTransferStatistics() {
        return baseMapper.selectTransferStatistics();
    }

    @Override
    public List<TransferRecordExcel> exportTransferRecord(String fromUsername, String toUsername, Integer status,
                                                          String startDate, String endDate) {
        return baseMapper.selectTransferRecordForExport(fromUsername, toUsername, status, startDate, endDate);
    }
} 