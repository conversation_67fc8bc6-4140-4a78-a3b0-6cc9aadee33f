<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.TransferRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.admin.entity.TransferRecord">
        <id column="id" property="id" />
        <result column="from_user_id" property="fromUserId" />
        <result column="from_username" property="fromUsername" />
        <result column="from_email" property="fromEmail" />
        <result column="to_user_id" property="toUserId" />
        <result column="to_username" property="toUsername" />
        <result column="to_email" property="toEmail" />
        <result column="amount" property="amount" />
        <result column="fee" property="fee" />
        <result column="real_amount" property="realAmount" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, from_user_id, from_username, from_email, to_user_id, to_username, to_email, amount, fee, real_amount,
        status, create_time, update_time
    </sql>

    <!-- 分页查询转账记录 -->
    <select id="selectTransferRecordList" resultMap="BaseResultMap">
        SELECT 
            t.*,
            f.email as from_email,
            u.email as to_email
        FROM transfer_record t
        LEFT JOIN front_user f ON t.from_user_id = f.id
        LEFT JOIN front_user u ON t.to_user_id = u.id
        <where>
           t.from_user_id>1
            <if test="params.fromUsername != null and params.fromUsername != ''">
                AND (t.from_username = #{params.fromUsername} OR f.email = #{params.fromUsername})
            </if>
            <if test="params.toUsername != null and params.toUsername != ''">
                AND (t.to_username = #{params.toUsername} OR u.email = #{params.toUsername})
            </if>
            <if test="params.status != null and params.status != ''">
                AND t.status = #{params.status}
            </if>
            <if test="params.startDate != null and params.startDate != ''">
                AND t.create_time >= #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                AND t.create_time &lt;= #{params.endDate}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

    <!-- 查询转账统计信息 -->
    <select id="selectTransferStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            SUM(amount) as totalAmount,
            SUM(fee) as totalFee,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failCount
        FROM transfer_record where from_user_id>1
    </select>

    <!-- 查询转账记录(导出用) -->
    <select id="selectTransferRecordForExport" resultType="com.admin.entity.excel.TransferRecordExcel">
        SELECT 
            t.*,
            f.email as fromEmail,
            tu.email as toEmail
        FROM transfer_record t
        LEFT JOIN front_user f ON t.from_user_id = f.id
        LEFT JOIN front_user tu ON t.to_user_id = tu.id
        <where>
            t.from_user_id>1 and t.to_user_id>1
            <if test="fromUsername != null and fromUsername != ''">
                AND t.from_username LIKE CONCAT('%', #{fromUsername}, '%')
            </if>
            <if test="toUsername != null and toUsername != ''">
                AND t.to_username LIKE CONCAT('%', #{toUsername}, '%')
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="startDate != null and startDate != ''">
                AND t.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND t.create_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper> 