package com.admin.service.impl;

import com.admin.entity.ChainConfig;
import com.admin.mapper.ChainConfigMapper;
import com.admin.service.ChainConfigService;
import com.admin.utils.AESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ChainConfigServiceImpl implements ChainConfigService {
    @Autowired
    private ChainConfigMapper chainConfigMapper;

    @Override
    public ChainConfig getConfig() {
        return chainConfigMapper.selectByIdOne();
    }

    @Override
    public int updateConfig(ChainConfig config) {
        if (config.getBnbPrivateKey() != null && !config.getBnbPrivateKey().isEmpty()) {
            config.setBnbPrivateKey(AESUtil.encrypt(config.getBnbPrivateKey()));
        }
        if (config.getWithdrawPrivateKey() != null && !config.getWithdrawPrivateKey().isEmpty()) {
            config.setWithdrawPrivateKey(AESUtil.encrypt(config.getWithdrawPrivateKey()));
        }
        return chainConfigMapper.updateByIdOne(config);
    }
} 