package com.admin.service.impl;

import com.admin.mapper.FuturesOptionOrderMapper;
import com.admin.service.FuturesOptionOrderService;
import com.admin.vo.FuturesOptionOrderVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class FuturesOptionOrderServiceImpl implements FuturesOptionOrderService {
    @Resource
    private FuturesOptionOrderMapper futuresOptionOrderMapper;

    @Override
    public List<FuturesOptionOrderVO> selectPageVO(Map<String, Object> params) {
        return futuresOptionOrderMapper.selectPageVO(params);
    }

    @Override
    public FuturesOptionOrderVO selectDetailVO(Long id) {
        return futuresOptionOrderMapper.selectDetailVO(id);
    }
} 