package com.admin.service.impl;

import com.admin.entity.SysParams;
import com.admin.mapper.SysParamsMapper;
import com.admin.service.SysParamsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 系统参数配置
 */
@Service
public class SysParamsServiceImpl extends ServiceImpl<SysParamsMapper, SysParams> implements SysParamsService {

    @Override
    public SysParams getParams() {
        // 获取第一条记录
        LambdaQueryWrapper<SysParams> wrapper = new LambdaQueryWrapper<>();
        wrapper.last("LIMIT 1");
        SysParams params = baseMapper.selectOne(wrapper);
        
        // 如果没有记录，创建默认配置
        if (params == null) {
            params = createDefaultParams();
        }
        
        return params;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateParams(SysParams params) {
        // 参数校验
        validateParams(params);
        
        // 设置更新时间
        params.setUpdateTime(new Date());
        
        // 如果是新增
        if (params.getId() == null) {
            params.setId(1L);
            params.setCreateTime(new Date());
            baseMapper.insert(params);
        } else {
            baseMapper.updateById(params);
        }
    }
    
    /**
     * 创建默认参数配置
     */
    private SysParams createDefaultParams() {
        SysParams params = new SysParams();
        // 转账设置
        params.setMinTransfer(new BigDecimal("100.00"));
        params.setMaxTransfer(new BigDecimal("50000.00"));
        params.setTransferFee(new BigDecimal("1.00"));
        params.setEnableTransfer(1);
        params.setEnableInternalTransfer(1);
        
        // 提现设置
        params.setMinWithdraw(new BigDecimal("100.00"));
        params.setMaxWithdraw(new BigDecimal("50000.00"));
        params.setMaxAutoWithdraw(new BigDecimal("200.00"));
        params.setWithdrawFee(new BigDecimal("1.00"));
        params.setEnableWithdraw(1);
        params.setAutoWithdraw(1);
        
        // 跟单设置
        params.setMinCopyTrade(new BigDecimal("900.00"));
        params.setTradeProfitRate(new BigDecimal("50.00"));
        params.setCopyTradeAccountRate(new BigDecimal("5.00"));
        params.setCopyTradeFee(new BigDecimal("0.04"));
        params.setPlatformFeeRate(new BigDecimal("40.00"));
        
        // 设置时间
        Date now = new Date();
        params.setCreateTime(now);
        params.setUpdateTime(now);
        
        baseMapper.insert(params);
        return params;
    }
    
    /**
     * 参数校验
     */
    private void validateParams(SysParams params) {
        // 转账限额校验
        if (params.getMinTransfer() != null && params.getMaxTransfer() != null 
            && params.getMinTransfer().compareTo(params.getMaxTransfer()) > 0) {
            throw new RuntimeException("最低转账限额不能大于最高转账限额");
        }
        
        // 提现限额校验
        if (params.getMinWithdraw() != null && params.getMaxWithdraw() != null 
            && params.getMinWithdraw().compareTo(params.getMaxWithdraw()) > 0) {
            throw new RuntimeException("最低提现限额不能大于最高提现限额");
        }
        
        // 自动提现限额校验
        if (params.getMaxAutoWithdraw() != null && params.getMaxWithdraw() != null 
            && params.getMaxAutoWithdraw().compareTo(params.getMaxWithdraw()) > 0) {
            throw new RuntimeException("自动提现限额不能大于最高提现限额");
        }
        
        // 费率校验（0-100）
        validateRate(params.getTransferFee(), "转账手续费");
        validateRate(params.getWithdrawFee(), "提现手续费");
        validateRate(params.getTradeProfitRate(), "交易盈利比例");
        validateRate(params.getCopyTradeAccountRate(), "跟单账户比例");
        validateRate(params.getCopyTradeFee(), "跟单手续费比例");
        validateRate(params.getPlatformFeeRate(), "平台手续费比例");
    }
    
    /**
     * 费率校验
     */
    private void validateRate(BigDecimal rate, String name) {
        if (rate != null && (rate.compareTo(BigDecimal.ZERO) < 0 || rate.compareTo(new BigDecimal("100")) > 0)) {
            throw new RuntimeException(name + "必须在0-100之间");
        }
    }
} 