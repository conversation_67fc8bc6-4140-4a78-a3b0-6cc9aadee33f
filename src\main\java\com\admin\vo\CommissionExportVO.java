package com.admin.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@HeadRowHeight(20)  // 表头行高
@HeadStyle(fillBackgroundColor = 24)  // 设置表头背景色为灰色
@HeadFontStyle(fontHeightInPoints = 11)  // 表头字体样式
public class CommissionExportVO {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private Integer index;
    
    @ExcelProperty(value = "用户名称", index = 1)
    @ColumnWidth(15)
    private String username;
    
    @ExcelProperty(value = "手机号码", index = 2)
    @ColumnWidth(15)
    private String phone;
    
    @ExcelProperty(value = "赠送类型", index = 3)
    @ColumnWidth(15)
    private Integer commissionType;
    
    @ExcelProperty(value = "赠送流量", index = 4)
    @ColumnWidth(15)
    private BigDecimal commissionAmount;
    
    @ExcelProperty(value = "赠送时间", index = 5)
    @ColumnWidth(20)
    private LocalDateTime releaseTime;
} 