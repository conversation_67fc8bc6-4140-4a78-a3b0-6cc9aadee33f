<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.SysParamsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.admin.entity.SysParams">
        <id column="id" property="id" />
        <result column="min_transfer" property="minTransfer" />
        <result column="max_transfer" property="maxTransfer" />
        <result column="transfer_fee" property="transferFee" />
        <result column="enable_transfer" property="enableTransfer" />
        <result column="min_withdraw" property="minWithdraw" />
        <result column="max_withdraw" property="maxWithdraw" />
        <result column="withdraw_fee" property="withdrawFee" />
        <result column="enable_withdraw" property="enableWithdraw" />
        <result column="account_name" property="accountName" />
        <result column="account_number" property="accountNumber" />
        <result column="bank_name" property="bankName" />
        <result column="bank_branch" property="bankBranch" />
        <result column="bank_remark" property="bankRemark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="auto_withdraw" property="autoWithdraw" />
        <result column="auto_exchange" property="autoExchange" />        
        <result column="reward_1" property="reward1" />
         <result column="reward_2" property="reward2" />
         <result column="reward_3" property="reward3" />
         <result column="reward_4" property="reward4" />
         <result column="reward_5" property="reward5" />
        <result column="reward_6" property="reward6" />
        <result column="reward_7" property="reward7" />
         <result column="sum_rate" property="sumRate" />
         <result column="now_rate" property="nowRate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, min_transfer, max_transfer, transfer_fee, enable_transfer, 
        min_withdraw, max_withdraw, withdraw_fee, enable_withdraw,
        account_name, account_number, bank_name, bank_branch, bank_remark,
        create_time, update_time, auto_withdraw, enable_internal_transfer,auto_exchange,reward_1,reward_2,reward_3,reward_4,reward_5,reward_6,reward_7,sum_rate,now_rate
    </sql>

</mapper> 