package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("account_transfer_record")
public class AccountTransferRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;
    private String username;
    private String fromAccountType;
    private String toAccountType;
    private BigDecimal amount;
    private BigDecimal fromBalanceBefore;
    private BigDecimal fromBalanceAfter;
    private BigDecimal toBalanceBefore;
    private BigDecimal toBalanceAfter;
    private Integer status; // 0:处理中, 1:成功, 2:失败
    private String remark;
    private Date createTime;
    private Date updateTime;
    private String email;
} 