package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.RechargeRecord;
import com.admin.service.RechargeRecordService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/finance/recharge")
public class RechargeRecordController {

    @Autowired
    private RechargeRecordService rechargeRecordService;
    @Log(title = "充值明细", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        // 支持按tx_hash、recharge_type等字段查询
        IPage<RechargeRecord> page = rechargeRecordService.getRechargeRecordList(params);
        return R.ok()
                .put("total", page.getTotal())
                .put("data", page.getRecords());
    }

    @GetMapping("/statistics")
    public R statistics() {
        Map<String, Object> statistics = rechargeRecordService.getRechargeStatistics();
        return R.ok().data(statistics);
    }
    @Log(title = "充值明细", operType = "审核")
    @PostMapping("/audit/{id}")
    public R audit(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        Integer auditStatus = (Integer) params.get("auditStatus");
        String remark = (String) params.get("remark");
        boolean success = rechargeRecordService.auditRecharge(id, auditStatus, remark);
        return success ? R.ok() : R.error("审核失败");
    }

    @Log(title = "充值明细", operType = "导出")
    @PostMapping("/export")
    public void export(@RequestBody Map<String, Object> params, HttpServletResponse response) throws IOException {
        try {
            // 查询数据
            List<RechargeRecord> list = rechargeRecordService.getRechargeRecordExportList(params);

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("充值记录");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            
            // 设置表头字体
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            
            // 设置表头边框
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            // 创建内容样式
            CellStyle contentStyle = workbook.createCellStyle();
            contentStyle.setAlignment(HorizontalAlignment.CENTER);
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentStyle.setBorderTop(BorderStyle.THIN);
            contentStyle.setBorderBottom(BorderStyle.THIN);
            contentStyle.setBorderLeft(BorderStyle.THIN);
            contentStyle.setBorderRight(BorderStyle.THIN);

            // 创建日期格式样式
            CellStyle dateCellStyle = workbook.createCellStyle();
            dateCellStyle.cloneStyleFrom(contentStyle);
            CreationHelper createHelper = workbook.getCreationHelper();
            dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd HH:mm:ss"));

            // 创建金额样式
            CellStyle amountStyle = workbook.createCellStyle();
            amountStyle.cloneStyleFrom(contentStyle);
            amountStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));

            // 设置表头
            String[] headers = {"用户名", "邮箱", "充值金额(元)", "充值类型", "审核状态", "充值时间", "备注"};
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 400); // 设置表头行高
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            int rowNum = 1;
            for (RechargeRecord record : list) {
                Row row = sheet.createRow(rowNum++);
                row.setHeight((short) 350); // 设置内容行高
                
                // 用户名
                Cell usernameCell = row.createCell(0);
                usernameCell.setCellValue(record.getUsername() != null ? record.getUsername() : "");
                usernameCell.setCellStyle(contentStyle);
                
                // 邮箱
                Cell emailCell = row.createCell(1);
                emailCell.setCellValue(record.getEmail() != null ? record.getEmail() : "");
                emailCell.setCellStyle(contentStyle);
                
                // 充值金额
                Cell amountCell = row.createCell(2);
                if (record.getAmount() != null) {
                    amountCell.setCellValue(record.getAmount().doubleValue());
                    amountCell.setCellStyle(amountStyle);
                } else {
                    amountCell.setCellValue("");
                    amountCell.setCellStyle(contentStyle);
                }
                
                // 充值类型
                Cell typeCell = row.createCell(3);
                typeCell.setCellValue(record.getRechargeType() == 2 ? "后台充值" : "用户充值");
                typeCell.setCellStyle(contentStyle);
                
                // 审核状态
                Cell statusCell = row.createCell(4);
                statusCell.setCellValue(getAuditStatusText(record.getAuditStatus()));
                statusCell.setCellStyle(contentStyle);
                
                // 充值时间
                Cell dateCell = row.createCell(5);
                if (record.getCreateTime() != null) {
                    dateCell.setCellValue(record.getCreateTime());
                    dateCell.setCellStyle(dateCellStyle);
                } else {
                    dateCell.setCellValue("");
                    dateCell.setCellStyle(contentStyle);
                }
                
                // 备注
                Cell remarkCell = row.createCell(6);
                remarkCell.setCellValue(record.getRemark() != null ? record.getRemark() : "");
                remarkCell.setCellStyle(contentStyle);
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
                // 设置最小宽度
                int currentWidth = sheet.getColumnWidth(i);
                if (currentWidth < 3000) {
                    sheet.setColumnWidth(i, 3000);
                } else if (currentWidth > 15000) {
                    // 设置最大宽度
                    sheet.setColumnWidth(i, 15000);
                }
            }

            // 设置响应头
            String fileName = URLEncoder.encode("充值记录_" + System.currentTimeMillis() + ".xlsx", "UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(R.error("导出失败：" + e.getMessage()));
        }
    }

    private String getAuditStatusText(Integer status) {
        switch (status) {
            case 0:
                return "待审核";
            case 1:
                return "已通过";
            case 2:
                return "已拒绝";
            default:
                return "未知";
        }
    }

    @Log(title = "充值趋势", operType = "查询")
    @GetMapping("/trend")
    public R rechargeTrend(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        // 默认近7天
        if (startDate == null || endDate == null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date now = new Date();
            endDate = sdf.format(now);
            Date start = new Date(now.getTime() - 6L * 24 * 60 * 60 * 1000); // 近7天
            startDate = sdf.format(start);
        }
        List<Map<String, Object>> trend = rechargeRecordService.getRechargeTrend(startDate, endDate);
        return R.ok().put("data", trend);
    }
} 