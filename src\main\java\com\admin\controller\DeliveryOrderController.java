package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.service.DeliveryOrderService;
import com.admin.vo.DeliveryOrderVO;
import com.admin.common.utils.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/deliveryOrder")
public class DeliveryOrderController {
    @Resource
    private DeliveryOrderService deliveryOrderService;

    @Log(title = "交割订单", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam(defaultValue = "1") int pageNum,
                  @RequestParam(defaultValue = "10") int pageSize,
                  @RequestParam(required = false) String symbol,
                  @RequestParam(required = false) String userNo,
                  @RequestParam(required = false) String leaderNo,
                  @RequestParam(required = false) Integer rebateStatus,
                  @RequestParam(required = false) Integer status,
                  @RequestParam(required = false) Integer profitStatus,
                  @RequestParam(required = false) Integer direction,
                  @RequestParam(required = false) Integer isSettlement) {
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        if (symbol != null && !symbol.isEmpty()) params.put("symbol", symbol);
        if (userNo != null && !userNo.isEmpty()) params.put("userNo", userNo);
        if (leaderNo != null && !leaderNo.isEmpty()) params.put("leaderNo", leaderNo);
        if (rebateStatus != null) params.put("rebateStatus", rebateStatus);
        if (status != null) params.put("status", status);
        if (profitStatus != null) params.put("profitStatus", profitStatus);
        if (direction != null) params.put("direction", direction);
        if (isSettlement != null) params.put("isSettlement", isSettlement);
        IPage<DeliveryOrderVO> page = deliveryOrderService.selectPageVO(params);
        return R.ok().put("data", new HashMap<String, Object>() {{
            put("records", page.getRecords());
            put("total", page.getTotal());
        }});
    }

    @GetMapping("/detail")
    public R detail(@RequestParam Long id) {
        DeliveryOrderVO vo = deliveryOrderService.selectDetailVO(id);
        return R.ok().put("data", vo);
    }

    /**
     * 一键补仓
     */
    @Log(title = "交割订单", operType = "一键补仓")
    @PostMapping("/oneClickReplenish")
    public R oneClickReplenish() {
        try {
            log.info("执行一键补仓操作");
            Map<String, Object> result = deliveryOrderService.oneClickReplenish();

            if ((Boolean) result.get("success")) {
                return R.ok().put("msg", result.get("message")).put("data", result);
            } else {
                return R.error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("一键补仓操作失败", e);
            return R.error("一键补仓操作失败：" + e.getMessage());
        }
    }
}