package com.admin.service;

import com.admin.entity.CommissionRecord;
import com.admin.vo.CommissionStats;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.time.LocalDateTime;
import java.util.List;
import java.math.BigDecimal;

public interface CommissionRecordService extends IService<CommissionRecord> {
    IPage<CommissionRecord> getCommissionList(String keyword, String commissionType,
                                            LocalDateTime startDate, LocalDateTime endDate,
                                            Integer page, Integer limit);
    
    CommissionStats getCommissionStats(LocalDateTime startTime, LocalDateTime endTime);

    List<CommissionRecord> getExportList(String keyword, String commissionType,
                                       LocalDateTime startDate, LocalDateTime endDate);

    BigDecimal calculateFilteredTotal(String keyword, String commissionType, 
                                    LocalDateTime startDate, LocalDateTime endDate);
} 