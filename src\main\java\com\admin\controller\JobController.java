package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.Result;
import com.admin.entity.SysJob;
import com.admin.entity.SysJobLog;
import com.admin.job.ScheduleManager;
import com.admin.service.JobService;
import com.admin.service.JobLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/job")
@CrossOrigin
public class JobController {
    
    @Autowired
    private JobService jobService;
    
    @Autowired
    private ScheduleManager scheduleManager;
    
    @Autowired
    private JobLogService jobLogService;
    
    /**
     * 获取任务列表
     */
    @GetMapping("/list")
    public Result list(SysJob job) {
        List<SysJob> list = jobService.selectJobList(job);
        return Result.ok()
            .data("rows", list)
            .data("total", list.size());
    }
    
    /**
     * 获取任务详细信息
     */
    @GetMapping("/{jobId}")
    public Result getInfo(@PathVariable Long jobId) {
        return Result.ok()
            .data("data", jobService.selectJobById(jobId));
    }
    
    /**
     * 新增任务
     */
     @Log(title = "定时任务", operType = "新增")
    @PostMapping
    public Result add(@RequestBody SysJob job) {
        int rows = jobService.insertJob(job);
        return rows > 0 ? Result.ok() : Result.error("新增任务失败");
    }
    
    /**
     * 修改任务
     */
    @Log(title = "定时任务", operType = "修改")
    @PostMapping("/update")
    public Result edit(@RequestBody SysJob job) {
        try {
            // 1. 获取任务当前状态
            SysJob oldJob = jobService.selectJobById(job.getId());
            boolean isRunning = oldJob != null && oldJob.getStatus() == 1;
            
            // 2. 如果任务正在运行，先停止旧任务
            if (isRunning) {
                scheduleManager.stopJob(job.getId());
            }
            
            // 3. 更新任务信息
            int rows = jobService.updateJob(job);
            if (rows <= 0) {
                return Result.error("修改任务失败");
            }
            
            // 4. 如果之前是运行状态，用新规则重启任务
            if (isRunning) {
                String[] invokeTarget = job.getInvokeTarget().split("\\.");
                scheduleManager.startJob(job.getId(), 
                    invokeTarget[0],
                    invokeTarget[1].replace("()", ""),
                    job.getCronExpression());
            }
            
            return Result.ok().data("rows", rows);
        } catch (Exception e) {
            return Result.error("修改任务失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除任务
     */
    @Log(title = "定时任务", operType = "删除")
    @PostMapping("/delete/{id}")
    public Result remove(@PathVariable("id") Long jobId) {
        try {
            // 1. 停止并清理任务
            scheduleManager.stopJob(jobId);
            
            // 2. 从数据库删除任务
            int rows = jobService.deleteJobById(jobId);
            
            // 3. 确保任务已经完全清理
            if (scheduleManager.exists(jobId)) {
                return Result.error("任务清理失败，请重试");
            }
            
            return rows > 0 ? Result.ok() : Result.error("删除失败");
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 立即执行任务
     */
    @Log(title = "定时任务", operType = "执行")
    @PostMapping("/execute/{jobId}")
    public Result run(@PathVariable Long jobId) {
        SysJob job = jobService.selectJobById(jobId);
        // 检查任务状态
        if (job.getStatus() == 0) {
            return Result.error("任务已暂停，请先启动任务");
        }
        
        // 检查任务是否正在执行
        if (scheduleManager.isRunning(jobId)) {
            return Result.error("任务正在执行中，请稍后再试");
        }
        
        try {
            String[] invokeTarget = job.getInvokeTarget().split("\\.");
            scheduleManager.runOnce(jobId, invokeTarget[0], invokeTarget[1]);
            return Result.ok();
        } catch (Exception e) {
            return Result.error("任务执行失败：" + e.getMessage());
        }
    }
    
    /**
     * 暂停/恢复任务
     */
    @Log(title = "定时任务", operType = "状态")
    @PostMapping("/status/{id}/{status}")
    public Result changeStatus(@PathVariable("id") Long jobId, @PathVariable("status") Integer status) {
        SysJob job = jobService.selectJobById(jobId);
        if (status == 1) {
            // 启动任务
            String[] invokeTarget = job.getInvokeTarget().split("\\.");
            scheduleManager.startJob(jobId, invokeTarget[0], 
                invokeTarget[1].replace("()", ""), 
                job.getCronExpression());
        } else {
            // 停止任务
            scheduleManager.stopJob(jobId);
        }
        
        job.setStatus(status);
        jobService.updateJob(job);
        return Result.ok();
    }
    
    /**
     * 获取任务日志列表
     */
    @GetMapping("/log/list")
    public Result getJobLogList(@RequestParam Long jobId, 
                              @RequestParam(defaultValue = "1") Integer page,
                              @RequestParam(defaultValue = "10") Integer limit) {
        try {
            Page<SysJobLog> pageParam = new Page<>(page, limit);
            IPage<SysJobLog> pageModel = jobLogService.lambdaQuery()
                .eq(SysJobLog::getJobId, jobId)
                .orderByDesc(SysJobLog::getExecutionTime)
                .page(pageParam);
                
            return Result.ok()
                .data("rows", pageModel.getRecords())
                .data("total", pageModel.getTotal());
        } catch (Exception e) {
            return Result.error("获取日志列表失败：" + e.getMessage());
        }
    }
} 