package com.admin.mapper;

import com.admin.entity.FrontUser;
import com.admin.model.query.FrontUserQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface FrontUserMapper extends BaseMapper<FrontUser> {
    
    /**
     * 分页查询用户列表
     */
    IPage<FrontUser> getUserList(Page<FrontUser> page, @Param("query") FrontUserQuery query);
    
    /**
     * 更新用户可用余额
     */
    int updateAvailableBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
    int updateAvailableBalanceCZ(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 更新用户跟单账户余额
     */
    int updateCopyTradeBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 更新用户储备金额
     */
    int updateReserveAmount(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    
    /**
     * 根据手机号查询用户
     */
    FrontUser selectByPhone(@Param("phone") String phone);
    
    /**
     * 根据用户编号查询用户
     */
    FrontUser selectByUserNo(@Param("userNo") String userNo);
    
    /**
     * 根据分享码查询用户
     */
    FrontUser selectByShareCode(@Param("shareCode") String shareCode);
    
    /**
     * 根据推荐码查询用户
     */
    FrontUser selectByReferrerCode(@Param("referrerCode") String referrerCode);
    
    /**
     * 获取用户详情（包含代理级别名称）
     */
    FrontUser getUserDetail(@Param("id") Long id);
    
    /**
     * 获取团队设备总数
     */
    Integer getTeamDeviceCount(String shareCode);
    
    /**
     * 获取直接下级用户列表
     */
    List<FrontUser> getSubordinateUsers(@Param("shareCode") String shareCode);
    
    /**
     * 根据邮箱查询用户（包含代理级别）
     */
    FrontUser getUserByEmail(@Param("email") String email);
    
    /**
     * 获取顶级用户列表（包含代理级别）
     */
    List<FrontUser> getTopUsers();
    
    /**
     * 重置团队今日统计
     */
    int resetTeamTodayCount();

    int updatePasswordById(@Param("id") Long id, @Param("password") String password);

    /**
     * 更新用户激活状态
     */
    int updateActivatedStatus(@Param("id") Long id, @Param("isActivated") Integer isActivated);

    /**
     * 查询用户总充值金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM recharge_record WHERE user_id = #{userId} AND audit_status = 1")
    BigDecimal getTotalRechargeAmount(@Param("userId") Long userId);

    /**
     * 检查用户是否已激活
     */
    @Select("SELECT is_activated FROM front_user WHERE id = #{userId}")
    Integer checkUserActivationStatus(@Param("userId") Long userId);

    /**
     * 激活用户账户（仅当未激活时）
     */
    @Update("UPDATE front_user SET is_activated = 1 WHERE id = #{userId} AND is_activated = 0")
    int activateUserAccount(@Param("userId") Long userId);

}