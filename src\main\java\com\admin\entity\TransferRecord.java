package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("transfer_record")
public class TransferRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long fromUserId;
    private String fromUsername;
    private String fromEmail;
    private Long toUserId;
    private String toUsername;
    private String toEmail;
    private BigDecimal amount;
    private BigDecimal fee;
    private BigDecimal realAmount;
    private Integer status;  // 0:处理中,1:成功,2:失败
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 