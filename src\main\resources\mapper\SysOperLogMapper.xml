<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.SysOperLogMapper">
    
    <select id="selectOperLogPage" resultType="com.admin.entity.SysOperLog">
        SELECT * FROM sys_oper_log
        <where>
            <if test="params.operName != null and params.operName != ''">
                AND oper_name LIKE CONCAT('%', #{params.operName}, '%')
            </if>
            <if test="params.title != null and params.title != ''">
                AND title LIKE CONCAT('%', #{params.title}, '%')
            </if>
            <if test="params.operType != null and params.operType != ''">
                AND oper_type = #{params.operType}
            </if>
            <if test="params.status != null and params.status != ''">
                AND status = #{params.status}
            </if>
            <if test="params.startDate != null and params.startDate != ''">
                AND oper_time >= #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                AND oper_time &lt;= #{params.endDate}
            </if>
        </where>
        ORDER BY oper_time DESC
    </select>
    
</mapper> 