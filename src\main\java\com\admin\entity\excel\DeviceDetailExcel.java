package com.admin.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.admin.common.converter.IndexConverter;
import com.admin.common.converter.DeviceStatusConverter;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 42)
@HeadRowHeight(25)
public class DeviceDetailExcel {
    @ExcelProperty(value = "序号", converter = IndexConverter.class)
    @ColumnWidth(8)
    private Integer index;
    
    @ExcelProperty(value = "设备编号")
    @ColumnWidth(20)
    private String deviceNo;
    
    @ExcelProperty(value = "每日收益")
    @ColumnWidth(15)
    private BigDecimal dailyProfit;
    
    @ExcelProperty(value = "累计收益")
    @ColumnWidth(15)
    private BigDecimal totalProfit;
    
    @ExcelProperty(value = "设备状态", converter = DeviceStatusConverter.class)
    @ColumnWidth(10)
    private Integer status;
    
    @ExcelProperty(value = "购买时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;
    
    @ExcelProperty(value = "用户账号")
    @ColumnWidth(15)
    private String username;
    
    @ExcelProperty(value = "真实姓名")
    @ColumnWidth(15)
    private String realName;
    
    @ExcelProperty(value = "手机号码")
    @ColumnWidth(15)
    private String phone;
    
    @ExcelProperty(value = "省份")
    @ColumnWidth(15)
    private String province;
    
    @ExcelProperty(value = "城市")
    @ColumnWidth(15)
    private String city;
    
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;
} 