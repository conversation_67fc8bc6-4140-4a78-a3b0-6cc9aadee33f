package com.admin.aspect;

import com.admin.annotation.Log;
import com.admin.entity.SysOperLog;
import com.admin.service.SysOperLogService;
import com.admin.common.utils.SecurityUtils;
import com.admin.common.utils.ServletUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

@Aspect
@Component
public class LogAspect {

    @Autowired
    private SysOperLogService operLogService;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Pointcut("@annotation(com.admin.annotation.Log)")
    public void logPointCut() {}

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 判断是否为导出方法
        MethodSignature signature = (MethodSignature) point.getSignature();
        if (signature.getReturnType() == void.class && 
            (signature.getMethod().getName().toLowerCase().contains("export") || 
             signature.getMethod().getName().toLowerCase().contains("download"))) {
            // 直接执行方法,不记录日志
            return point.proceed();
        }
        
        Object result = null;
        try {
            // 执行方法
            result = point.proceed();
            // 记录正常日志
            handleLog(point, null, result);
        } catch (Exception e) {
            // 记录异常日志
            handleLog(point, e, null);
            throw e;
        }
        return result;
    }

    private void handleLog(ProceedingJoinPoint joinPoint, Exception e, Object jsonResult) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Log logAnnotation = method.getAnnotation(Log.class);

            SysOperLog operLog = new SysOperLog();
            
            // 设置操作信息
            operLog.setStatus(e == null ? 1 : 0);
            operLog.setErrorMsg(e != null ? e.getMessage() : "");
            operLog.setOperTime(LocalDateTime.now());
            
            // 设置请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                operLog.setOperIp(ServletUtils.getClientIP(request));
                operLog.setOperUrl(request.getRequestURI());
                operLog.setRequestMethod(request.getMethod());
                // 设置请求参数
                if (joinPoint.getArgs().length > 0) {
                    Object[] args = joinPoint.getArgs();
                    StringBuilder paramsBuilder = new StringBuilder("[");
                    for (int i = 0; i < args.length; i++) {
                        Object arg = args[i];
                        if (arg instanceof MultipartFile) {
                            MultipartFile file = (MultipartFile) arg;
                            paramsBuilder.append(String.format("{fileName:'%s', size:%d}", file.getOriginalFilename(), file.getSize()));
                        } else {
                            try {
                                paramsBuilder.append(objectMapper.writeValueAsString(arg));
                            } catch (Exception ex) {
                                paramsBuilder.append("{unserializableParam}");
                            }
                        }
                        if (i < args.length - 1) paramsBuilder.append(",");
                    }
                    paramsBuilder.append("]");
                    operLog.setOperParam(paramsBuilder.toString());
                }
            }
            
            // 设置操作人员
            String username = SecurityUtils.getUsername();
            operLog.setOperName(username);
            
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = method.getName();
            operLog.setMethod(className + "." + methodName + "()");
            
            // 设置标题和操作类型
            if (logAnnotation != null) {
                operLog.setTitle(logAnnotation.title());
                operLog.setOperType(logAnnotation.operType());
            }

            // 保存日志
            operLogService.save(operLog);
        } catch (Exception ex) {
            // 记录日志失败，不影响业务
            ex.printStackTrace();
        }
    }
} 