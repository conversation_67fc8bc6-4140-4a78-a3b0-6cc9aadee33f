package com.admin.controller;

import com.admin.entity.CopyConfig;
import com.admin.service.CopyConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/copyConfig")
public class CopyConfigController {
    @Autowired
    private CopyConfigService copyConfigService;

    @GetMapping("/list")
    public List<CopyConfig> list() {
        return copyConfigService.list();
    }

    @PostMapping("/update")
    public boolean update(@RequestBody CopyConfig config) {
        return copyConfigService.updateById(config);
    }
} 