package com.admin.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class CopyConfig {
    private Long id;
    private String name;
    private Integer copyType;
    private BigDecimal minFollowAmount;
    private BigDecimal maxFollowAmount;
    private Integer lockTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 