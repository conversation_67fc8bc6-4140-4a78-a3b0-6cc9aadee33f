package com.admin.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.admin.common.converter.IndexConverter;
import com.admin.common.converter.TransferStatusConverter;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 42)  // 改为和提现明细一样的表头颜色
@HeadRowHeight(25)
public class TransferRecordExcel {
    @ExcelProperty(value = "序号", converter = IndexConverter.class)
    @ColumnWidth(8)
    private Integer index;
    
    @ExcelProperty(value = "转出用户")
    @ColumnWidth(15)
    private String fromUsername;
    
    @ExcelProperty(value = "转出邮箱")
    @ColumnWidth(15)
    private String fromEmail;
    
    @ExcelProperty(value = "转入用户")
    @ColumnWidth(15)
    private String toUsername;
    
    @ExcelProperty(value = "转入邮箱")
    @ColumnWidth(15)
    private String toEmail;
    
    @ExcelProperty(value = "转账金额")
    @ColumnWidth(15)
    private BigDecimal amount;
    
    @ExcelProperty(value = "手续费")
    @ColumnWidth(15)
    private BigDecimal fee;
    
    @ExcelProperty(value = "实际到账")
    @ColumnWidth(15)
    private BigDecimal realAmount;
    
    @ExcelProperty(value = "状态", converter = TransferStatusConverter.class)
    @ColumnWidth(10)
    private Integer status;
    
    @ExcelProperty(value = "转账时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;
} 