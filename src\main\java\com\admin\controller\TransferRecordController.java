package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.entity.TransferRecord;
import com.admin.service.TransferRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import java.util.List;
import java.net.URLEncoder;
import javax.servlet.http.HttpServletResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.admin.entity.excel.TransferRecordExcel;

@Slf4j
@RestController
@RequestMapping("/finance/transfer")
public class TransferRecordController {

    @Autowired
    private TransferRecordService transferRecordService;
    @Log(title = "转账明细", operType = "查询")
    @GetMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        log.info("接收到查询请求参数: {}", params);
        try {
            IPage<TransferRecord> page = transferRecordService.getTransferRecordList(params);
            log.info("查询结果: total={}, records={}", page.getTotal(), page.getRecords());
            return R.ok()
                    .put("data", page.getRecords())
                    .put("total", page.getTotal());
        } catch (Exception e) {
            log.error("查询转账记录失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    public R statistics() {
        try {
            Map<String, Object> statistics = transferRecordService.getTransferStatistics();
            return R.ok().data(statistics);
        } catch (Exception e) {
            log.error("查询转账统计失败", e);
            return R.error("查询统计失败：" + e.getMessage());
        }
    }
    @Log(title = "内部转账", operType = "转出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                      @RequestParam(required = false) String fromUsername,
                      @RequestParam(required = false) String toUsername,
                      @RequestParam(required = false) Integer status,
                      @RequestParam(required = false) String startDate,
                      @RequestParam(required = false) String endDate) {
        try {
            List<TransferRecordExcel> list = transferRecordService.exportTransferRecord(
                fromUsername, toUsername, status, startDate, endDate);
            
            // 设置序号
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setIndex(i + 1);
            }
            
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("转账记录", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            EasyExcel.write(response.getOutputStream(), TransferRecordExcel.class)
                .sheet("转账记录")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(list);
                
        } catch (Exception e) {
            log.error("导出转账记录失败", e);
        }
    }
} 