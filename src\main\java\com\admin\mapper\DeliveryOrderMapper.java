package com.admin.mapper;

import com.admin.vo.DeliveryOrderVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.admin.entity.DeliveryOrder;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

public interface DeliveryOrderMapper extends BaseMapper<DeliveryOrder> {
    IPage<DeliveryOrderVO> selectPageVO(Page<DeliveryOrderVO> page, @Param("params") Map<String, Object> params);
    DeliveryOrderVO selectDetailVO(@Param("id") Long id);

    /**
     * 查询需要补仓的订单
     * 条件：status=2，rebate_status=1，profit_status=2，is_settlement=1
     */
    List<DeliveryOrder> selectReplenishOrders();

    /**
     * 更新订单的返利状态
     * @param orderId 订单ID
     * @param rebateStatus 返利状态 (1:未返 2:已返)
     * @return 更新的行数
     */
    int updateRebateStatus(@Param("orderId") Long orderId, @Param("rebateStatus") Integer rebateStatus);
}