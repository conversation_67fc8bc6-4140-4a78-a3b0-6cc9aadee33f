package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("recharge_record")
public class RechargeRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    private String username;
    private BigDecimal amount;
    private String email;
    private String txHash;
    private Integer rechargeType;  // 1:链上充值,2:后台充值
    private Integer auditStatus;   // 0:待审核,1:已通过,2:已拒绝
    private String proofImage;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 