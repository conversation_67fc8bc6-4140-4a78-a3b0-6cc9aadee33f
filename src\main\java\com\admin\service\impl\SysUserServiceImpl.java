package com.admin.service.impl;

import com.admin.entity.SysUser;
import com.admin.mapper.SysUserMapper;
import com.admin.service.SysUserService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.admin.entity.SysRole;
import com.admin.entity.SysUserRole;
import com.admin.mapper.SysRoleMapper;
import com.admin.mapper.SysUserRoleMapper;
import com.admin.common.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Override
    public IPage<SysUser> getUserPage(Integer pageNum, Integer pageSize, String username, Integer status) {
        Page<SysUser> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectUserPage(page, username, status);
    }

    @Override
    @Transactional
    public void addUser(SysUser user) {
        try {
            // 保存角色信息并从user对象中移除
            String roleKey = user.getRole();
            user.setRole(null);  // 重要：在插入前清除role字段
            
            // 加密密码
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            // 设置默认状态
            user.setStatus(1);
            // 插入用户基本信息
            baseMapper.insert(user);
            
            // 处理用户角色关系
            if (roleKey != null && !roleKey.trim().isEmpty()) {
                // 查询角色ID
                LambdaQueryWrapper<SysRole> roleWrapper = new LambdaQueryWrapper<>();
                roleWrapper.eq(SysRole::getRoleKey, roleKey);
                SysRole role = sysRoleMapper.selectOne(roleWrapper);
                
                if (role != null) {
                    // 插入用户角色关系
                    SysUserRole userRole = new SysUserRole();
                    userRole.setUserId(user.getId());
                    userRole.setRoleId(role.getId());
                    log.info("Inserting user role relation: userId={}, roleId={}", user.getId(), role.getId());
                    sysUserRoleMapper.insert(userRole);
                } else {
                    log.warn("Role not found for key: {}", roleKey);
                }
            }
        } catch (Exception e) {
            log.error("Error adding user with role", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void updateUser(SysUser user) {
        // 检查用户是否存在
        SysUser existUser = baseMapper.selectById(user.getId());
        if (existUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查用户名是否重复
        if (!existUser.getUsername().equals(user.getUsername())) {
            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUser::getUsername, user.getUsername());
            if (baseMapper.selectCount(wrapper) > 0) {
                throw new BusinessException("用户名已存在");
            }
        }
        
        // 不更新密码和创建时间
        user.setPassword(null);
        user.setCreateTime(null);
        
        // 更新用户信息
        baseMapper.updateById(user);
        
        // 先删除原有角色关系
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserId, user.getId());
        sysUserRoleMapper.delete(wrapper);
        
        // 只有当角色不为空时才添加新角色
        if (StringUtils.hasText(user.getRole())) {
            // 根据角色key获取角色ID
            LambdaQueryWrapper<SysRole> roleWrapper = new LambdaQueryWrapper<>();
            roleWrapper.eq(SysRole::getRoleKey, user.getRole());
            SysRole role = sysRoleMapper.selectOne(roleWrapper);
            if (role != null) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(role.getId());
                sysUserRoleMapper.insert(userRole);
            }
        }
        // 如果role为空，则不添加角色关系，相当于取消角色
    }

    @Override
    @Transactional
    public void deleteUser(Long id) {
        // 先删除用户角色关系
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserId, id);
        sysUserRoleMapper.delete(wrapper);
        
        // 再删除用户
        baseMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void updateUserStatus(Long id, Integer status) {
        SysUser user = new SysUser();
        user.setId(id);
        user.setStatus(status);
        baseMapper.updateById(user);
    }

    @Override
    @Transactional
    public void resetPassword(Long id, String newPassword) {
        SysUser user = new SysUser();
        user.setId(id);
        user.setPassword(passwordEncoder.encode(newPassword));
        baseMapper.updateById(user);
    }

    @Override
    public SysUser getUserById(Long id) {
        return baseMapper.selectUserWithRole(id);
    }

    @Override
    public SysUser getUserByUsername(String username) {
        // 使用 MyBatis-Plus 的 lambdaQuery
        return this.lambdaQuery()
                .eq(SysUser::getUsername, username)
                .one();
    }
} 